---
title: >-
  چگونه صدا را از ویدیو با استفاده از پلیر VLC استخراج کنیم: راهنمای کامل برای
  مک و ویندوز
description: >-
  یاد بگیرید چگونه از فایل‌های ویدیویی بزرگ با استفاده از VLC Media Player در مک
  و ویندوز صدا استخراج کنید. این روش برای خدمات رونویسی هنگام کار با فایل‌های
  بالای ۲ گیگابایت مناسب است.
date: '2025-08-10'
slug: extract-audio-from-video-vlc-guide
image: /blog/extract-audio-from-video-vlc-guide/cover.jpg
author: David <PERSON>
tags:
  - VLC
  - audio extraction
  - video to audio
  - transcription
---
وقتی نیاز به رونویسی محتوای ویدئویی دارید، فقط به ترک صوتی نیاز دارید. برای فایل‌های ویدیویی بالای 2 گیگابایت، استخراج صوت به‌صورت محلی قبل از بارگذاری می‌تواند زمان قابل توجهی را صرفه‌جویی کند و فرآیند رونویسی را روان‌تر کند.

این راهنما به شما نشان می‌دهد که چگونه از VLC Media Player—یک ابزار رایگان که در هر دو سیستم‌عامل مک و ویندوز در دسترس است—برای استخراج صوت از فایل‌های ویدیویی خود برای رونویسی استفاده کنید.

## چرا قبل از رونویسی صوت را استخراج کنیم؟

برای فایل‌های ویدیویی بالای 2 گیگابایت، استخراج مبتنی بر مرورگر غیرقابل اعتماد می‌شود. این موضوع به‌ویژه زمانی صادق است که سرعت بارگذاری شما خیلی سریع نباشد—بارگذاری یک فایل 2 گیگابایتی می‌تواند 30 دقیقه تا یک ساعت یا حتی بیشتر طول بکشد. استخراج محلی با استفاده از VLC مزایای زیر را ارائه می‌دهد:

- **بارگذاری سریع‌تر**: فایل‌های صوتی معمولاً 10-15% اندازه فایل‌های ویدیویی هستند
- **قابلیت اطمینان**: VLC می‌تواند فایل‌های بزرگ را که مرورگرها نمی‌توانند پردازش کنند، مدیریت کند
- **کنترل کیفیت**: فرمت صوتی دقیق مورد نیاز خود را انتخاب کنید

## چه چیزهایی نیاز دارید

- VLC Media Player (دانلود رایگان از [videolan.org](https://www.videolan.org/vlc/))
- حداقل 2 گیگابایت فضای دیسک خالی
- فایل ویدیویی شما (VLC از فرمت‌های MP4، MOV، AVI، MKV و اکثر فرمت‌های دیگر پشتیبانی می‌کند)

## راهنمای گام به گام برای ویندوز

### گام 1: نصب VLC

VLC Media Player را از [videolan.org](https://www.videolan.org/vlc/) دانلود و نصب کنید

### گام 2: تبدیل ویدیو به صوت

1. VLC Media Player را باز کنید  
2. به **Media** → **Convert / Save** بروید (یا **Ctrl + R** را فشار دهید)  
3. روی **Add** کلیک کنید و فایل ویدیویی خود را انتخاب کنید  
4. روی **Convert / Save** کلیک کنید  
5. در منوی کشویی Profile، **Audio - MP3** را انتخاب کنید  
6. روی **Browse** کلیک کنید تا محل ذخیره فایل صوتی را انتخاب کنید  
7. روی **Start** کلیک کنید تا استخراج آغاز شود  

## راهنمای گام به گام برای مک  

### گام 1: نصب VLC  

VLC Media Player را از [videolan.org](https://www.videolan.org/vlc/) دانلود و نصب کنید  

### گام 2: تبدیل ویدیو به صدا  

1. VLC Media Player را باز کنید  
2. به **File** → **Convert / Stream** بروید (یا **⌘ + Alt + S** را فشار دهید)  
3. روی **Open Media** کلیک کرده و سپس **Add** را برای انتخاب فایل ویدیویی خود بزنید  
4. روی **Customize** کلیک کرده و **MP3** را در تب Encapsulation انتخاب کنید  
5. در تب Audio Codec، **Audio** را بررسی کرده و **MP3** را انتخاب کنید  
6. روی **Save as File** کلیک کنید، محل و نام فایل را انتخاب کنید  
7. روی **Save** کلیک کنید تا استخراج آغاز شود  

## نکات  

- **برای گفتار**: از فرمت MP3 استفاده کنید (حجم فایل کوچکتر)  
- **برای کیفیت بالا**: از فرمت WAV استفاده کنید (حجم فایل بزرگتر)  
- **فایل‌های بزرگ**: اطمینان حاصل کنید که فضای دیسک کافی دارید (حداقل 2 برابر حجم فایل ویدیویی)  
- **رفع اشکال**: اگر تبدیل ناموفق بود، فضای دیسک را بررسی کرده و فرمت خروجی متفاوتی را امتحان کنید  

## راهنمای حجم فایل  

- **زیر 2GB**: استخراج خودکار کار می‌کند (نیازی به این راهنما نیست)  
- **بیش از 2GB**: از این روش VLC استفاده کنید (برای تمام فایل‌های بزرگ توصیه می‌شود)

**نتایج مورد انتظار**: یک ویدیو ۲ گیگابایتی معمولاً به یک فایل صوتی حدود ۱۰۰ مگابایت تبدیل می‌شود. از آنجا که فایل‌های صوتی استخراج شده به مراتب کوچکتر از ویدیوی اصلی هستند، معمولاً از محدودیت‌های پلتفرم فراتر نمی‌روند حتی برای ویدیوهای منبع بسیار بزرگ.

## نتیجه‌گیری

استخراج صدا از فایل‌های ویدیویی بزرگ با استفاده از VLC Media Player یک تکنیک ساده اما قدرتمند است که می‌تواند به طور قابل توجهی روند ترنسکریپشن شما را بهبود بخشد. با پردازش فایل‌ها به صورت محلی قبل از بارگذاری، زمان صرفه‌جویی می‌کنید، استفاده از پهنای باند را کاهش می‌دهید و نتایج قابل اعتمادی را حتی با فایل‌های بسیار بزرگ تضمین می‌کنید.

این روش به ویژه برای حرفه‌ای‌ها که با محتوای طولانی مانند سخنرانی‌ها، جلسات، مصاحبه‌ها یا وبینارها سر و کار دارند، ارزشمند است. چند دقیقه‌ای که صرف استخراج صدا می‌شود می‌تواند ساعت‌ها زمان بارگذاری را صرفه‌جویی کند و تجربه ترنسکریپشن بسیار روان‌تری را فراهم کند.

به یاد داشته باشید: در حالی که این مرحله دستی یک فرآیند اضافی به روند کار شما اضافه می‌کند، تنها برای فایل‌های بالای ۲ گیگابایت ضروری است. برای فایل‌های کوچکتر، UniScribe به طور خودکار تمام پیش‌پردازش‌ها را در مرورگر شما انجام می‌دهد و بهترین حالت را برای شما فراهم می‌کند—راحتی برای فایل‌های کوچک و قابلیت اطمینان برای فایل‌های بزرگ.

آماده‌اید که امتحان کنید؟ VLC Media Player را دانلود کنید و این روش را با فایل ویدیویی بزرگ بعدی خود آزمایش کنید. آینده شما از زمانی که صرفه‌جویی کرده‌اید، از شما تشکر خواهد کرد!
