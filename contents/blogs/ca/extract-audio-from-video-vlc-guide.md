---
title: >-
  Com com extreure àudio d'un vídeo utilitzant VLC Player: Guia complet per a
  Mac i Windows
description: >-
  Aprèn a extreure àudio de grans fitxers de vídeo utilitzant VLC Media Player
  en Mac i Windows. Perfecte per a serveis de transcripció quan es tracta de
  fitxers de més de 2GB.
date: '2025-08-10'
slug: extract-audio-from-video-vlc-guide
image: /blog/extract-audio-from-video-vlc-guide/cover.jpg
author: <PERSON>
tags:
  - VLC
  - audio extraction
  - video to audio
  - transcription
---
Quan necessiteu transcriure contingut de vídeo, només necessiteu la pista d'àudio. Per a fitxers de vídeo de més de 2GB, extreure l'àudio localment abans de pujar-lo pot estalviar un temps significatiu i assegurar un procés de transcripció més fluid.

Aquesta guia us mostra com utilitzar VLC Media Player—una eina gratuïta disponible tant per a Mac com per a Windows—per extreure àudio dels vostres fitxers de vídeo per a la transcripció.

## Per què extreure àudio abans de la transcripció?

Per a fitxers de vídeo de més de 2GB, l'extracció basada en el navegador esdevé poc fiable. Això és especialment cert quan la vostra velocitat de pujada no és molt ràpida—pujar un fitxer de 2GB pot trigar de 30 minuts a una hora o fins i tot més. L'extracció local mitjançant VLC ofereix:

- **Pujades més ràpides**: Els fitxers d'àudio són típicament del 10-15% de la mida dels fitxers de vídeo
- **Fiabilitat**: VLC pot gestionar fitxers grans que els navegadors no poden processar
- **Control de qualitat**: Trieu el format d'àudio exacte per a les vostres necessitats

## El que necessitareu

- VLC Media Player (descarrega gratuïta de [videolan.org](https://www.videolan.org/vlc/))
- Almenys 2GB d'espai lliure al disc
- El vostre fitxer de vídeo (VLC suporta MP4, MOV, AVI, MKV i la majoria d'altres formats)

## Guia pas a pas per a Windows

### Pas 1: Instal·lar VLC

Descarregueu i instal·leu VLC Media Player des de [videolan.org](https://www.videolan.org/vlc/)

### Pas 2: Convertir vídeo a àudio

1. Obre VLC Media Player  
2. Ves a **Mitjà** → **Convertir / Desar** (o prem **Ctrl + R**)  
3. Fes clic a **Afegir** i selecciona el teu fitxer de vídeo  
4. Fes clic a **Convertir / Desar**  
5. A la llista desplegable de Perfil, selecciona **Àudio - MP3**  
6. Fes clic a **Explorar** per triar on desar el fitxer d'àudio  
7. Fes clic a **Iniciar** per començar l'extracció  

## Guia Pas a Pas per Mac  

### Pas 1: Instal·la VLC  

Descarrega i instal·la VLC Media Player des de [videolan.org](https://www.videolan.org/vlc/)  

### Pas 2: Converteix Vídeo a Àudio  

1. Obre VLC Media Player  
2. Ves a **Fitxer** → **Convertir / Transmetre** (o prem **⌘ + Alt + S**)  
3. Fes clic a **Obrir Mitjà** i després **Afegir** per seleccionar el teu fitxer de vídeo  
4. Fes clic a **Personalitzar** i selecciona **MP3** a la pestanya d'Encapsulació  
5. A la pestanya de Codec d'Àudio, marca **Àudio** i selecciona **MP3**  
6. Fes clic a **Desa com a Fitxer**, tria la ubicació i el nom del fitxer  
7. Fes clic a **Desa** per començar l'extracció  

## Consells  

- **Per a veu**: Utilitza el format MP3 (fitxer de mida més petita)  
- **Per a alta qualitat**: Utilitza el format WAV (fitxer de mida més gran)  
- **Fitxers grans**: Assegura't de tenir prou espai lliure al disc (almenys 2x la mida del fitxer de vídeo)  
- **Solució de problemes**: Si la conversió falla, comprova l'espai al disc i prova un format de sortida diferent  

## Directrius de Mida de Fitxer  

- **Menys de 2GB**: L'extracció automàtica funciona (no cal aquesta guia)  
- **Més de 2GB**: Utilitza aquest mètode VLC (recomanat per a tots els fitxers grans)

**Resultats esperats**: Un vídeo de 2GB es converteix típicament en un fitxer d'àudio d'uns ~100MB. Com que els fitxers d'àudio extrets són molt més petits que el vídeo original, normalment no superaran els límits de la plataforma fins i tot per a vídeos fonts molt grans.

## Conclusió

Extreure àudio de fitxers de vídeo grans utilitzant VLC Media Player és una tècnica senzilla però poderosa que pot millorar significativament el teu flux de treball de transcripció. Processant fitxers localment abans de la càrrega, estalvies temps, redueixes l'ús d'ample de banda i assegures resultats fiables fins i tot amb fitxers molt grans.

Aquesta mètode és particularment valuós per a professionals que tracten amb contingut de llarga durada com conferències, reunions, entrevistes o webinars. Els pocs minuts dedicats a extreure àudio poden estalviar hores en temps de càrrega i proporcionar una experiència de transcripció molt més fluida.

Recorda: mentre que aquest pas manual afegeix un procés extra al teu flux de treball, només és necessari per a fitxers de més de 2GB. Per a fitxers més petits, UniScribe gestiona automàticament tot el preprocessament al teu navegador, donant-te el millor de tots dos mons: comoditat per a fitxers petits i fiabilitat per a grans.

Preparat per provar-ho? Descarrega VLC Media Player i fes una prova d'aquest mètode amb el teu proper fitxer de vídeo gran. El teu jo futur t'agrairà pel temps estalviat!
