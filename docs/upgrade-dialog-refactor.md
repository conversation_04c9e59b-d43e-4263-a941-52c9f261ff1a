# UpgradeDialog 重构文档

## 问题背景

在原有的实现中，应用存在多个 `UpgradeDialog` 实例同时渲染的问题，导致 Radix UI Portal 冲突，使得某些 UpgradeDialog 无法正常显示。

### 具体问题

- **Sidebar 组件**中有一个 UpgradeDialog 实例
- **ApiManagementSection 组件**中有另一个 UpgradeDialog 实例
- **其他多个组件**也各自维护独立的 UpgradeDialog 实例

当多个相同的 Radix UI Dialog 组件同时存在时，它们的 Portal 会互相干扰，导致某些实例无法正常显示。

## 解决方案

采用 **全局 UpgradeDialog 管理** 的架构模式：

### 1. 全局状态管理

创建 `src/stores/useUpgradeDialogStore.js`：

```javascript
import { create } from "zustand";

export const useUpgradeDialogStore = create((set) => ({
  isOpen: false,
  source: "unknown",
  defaultPlanType: "yearly",
  title: "",
  description: "",
  showFreeTier: false,

  openDialog: (options = {}) =>
    set({
      isOpen: true,
      source: options.source || "unknown",
      defaultPlanType: options.defaultPlanType || "yearly",
      title: options.title || "",
      description: options.description || "",
      showFreeTier: options.showFreeTier || false,
    }),

  closeDialog: () =>
    set({
      isOpen: false,
    }),
}));
```

### 2. 全局 Dialog 组件

创建 `src/components/GlobalUpgradeDialog.jsx`：

```javascript
"use client";

import React, { useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { trackEvent } from "@/lib/analytics";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import Pricing from "@/components/Home/Pricing";
import Faq from "@/components/Home/Faq";

const GlobalUpgradeDialog = () => {
  const t = useTranslations("common.upgradeDialog");
  const pathname = usePathname();

  const {
    isOpen,
    source,
    defaultPlanType,
    title,
    description,
    showFreeTier,
    closeDialog,
  } = useUpgradeDialogStore();

  useEffect(() => {
    if (isOpen) {
      trackEvent("upgrade_dialog_open", {
        source: source,
      });
    }
  }, [isOpen, pathname, source]);

  return (
    <Dialog open={isOpen} onOpenChange={closeDialog}>
      <DialogContent
        className="max-w-full h-screen max-h-screen p-0 md:p-6"
        style={{ width: "90%", maxWidth: "90%" }}
      >
        <div className="h-full overflow-y-auto">
          <div className="container mx-auto py-6 px-4">
            <Pricing
              showFreeTier={showFreeTier}
              title={title || t("title")}
              description={description}
              defaultPlanType={defaultPlanType}
              titleMarginTop=""
              containerClassName="w-full dialog-pricing"
            />
            <Faq />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GlobalUpgradeDialog;
```

### 3. 集成到所有相关布局

由于应用中存在多个不同的布局系统，需要在每个布局中都添加 `GlobalUpgradeDialog`：

#### 3.1 DashboardLayout

在 `src/components/Dashboard/DashboardLayout/index.jsx` 中添加：

```javascript
import GlobalUpgradeDialog from "@/components/GlobalUpgradeDialog";

export default function DashboardLayout({ children, onFolderChange }) {
  return (
    <div className="flex h-screen overflow-hidden bg-white">
      <Sidebar />
      <div className="flex-1 overflow-y-auto p-4 md:p-8">{children}</div>

      {/* Global UpgradeDialog */}
      <GlobalUpgradeDialog />
    </div>
  );
}
```

#### 3.2 TranscriptionLayout

在 `src/components/Dashboard/TranscriptionV2/components/Layout.jsx` 中添加：

```javascript
import GlobalUpgradeDialog from "@/components/GlobalUpgradeDialog";

export function TranscriptionLayout({
  header,
  transcriptPanel,
  overviewPanel,
  audioPlayer,
}) {
  return (
    <div className="flex h-screen overflow-hidden bg-white">
      {/* ... 其他内容 */}

      {/* Global UpgradeDialog */}
      <GlobalUpgradeDialog />
    </div>
  );
}
```

#### 3.3 DashboardMobile

在 `src/components/Dashboard/DashboardMobile/index.jsx` 中添加：

```javascript
import GlobalUpgradeDialog from "@/components/GlobalUpgradeDialog";

export default function DashboardMobile() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* ... 其他内容 */}

      {/* Global UpgradeDialog */}
      <GlobalUpgradeDialog />
    </div>
  );
}
```

## 组件迁移

### 迁移前后对比

**迁移前：**

```javascript
// 每个组件都维护自己的状态
const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);

// 每个组件都渲染自己的 UpgradeDialog
<UpgradeDialog
  isOpen={showUpgradeDialog}
  onClose={() => setShowUpgradeDialog(false)}
  source="component_name"
/>;
```

**迁移后：**

```javascript
// 使用全局 store
const { openDialog } = useUpgradeDialogStore();

// 只需调用 openDialog 函数
const handleUpgradeClick = () => {
  openDialog({
    source: "component_name",
    defaultPlanType: "yearly",
  });
};

// 不再需要渲染 UpgradeDialog 组件
```

### 已迁移的组件列表

1. **ApiManagementSection** - API 管理页面
2. **Sidebar** - 左侧边栏
3. **DashboardV2/index.js** - 主仪表板
4. **DashboardMobile/index.jsx** - 移动端仪表板
5. **PremiumFeatureDialog** - 高级功能提示
6. **RemainingMinutes** - 剩余分钟数显示
7. **TranscriptionLimitDisplay** - 转录限制显示
8. **FeatureCheck** - 功能检查组件
9. **MobileAccount** - 移动端账户页面

## 技术要点

### 1. 单一实例原则

- 整个应用只有一个 UpgradeDialog 实例
- 避免了 Radix UI Portal 冲突

### 2. 全局状态管理

- 使用 Zustand 管理 dialog 状态
- 支持传递不同的参数（source、defaultPlanType 等）

### 3. 组件解耦

- 各个组件只负责触发 dialog
- 不负责渲染和管理 dialog 状态

### 4. 向后兼容

- 保持了原有的 API 接口
- 支持所有原有的配置选项

## 使用方法

### 基本用法

```javascript
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";

function MyComponent() {
  const { openDialog } = useUpgradeDialogStore();

  const handleUpgrade = () => {
    openDialog({
      source: "my_component",
      defaultPlanType: "yearly",
    });
  };

  return <button onClick={handleUpgrade}>Upgrade Plan</button>;
}
```

### 高级配置

```javascript
openDialog({
  source: "premium_feature",
  defaultPlanType: "onetime",
  title: "Custom Title",
  description: "Custom Description",
  showFreeTier: true,
});
```

## 优势

1. **解决冲突问题** - 彻底解决了多 Dialog 实例冲突
2. **代码简化** - 减少了重复的状态管理代码
3. **统一管理** - 所有 UpgradeDialog 行为统一管理
4. **易于维护** - 集中式的配置和状态管理
5. **性能优化** - 减少了不必要的组件渲染

## 注意事项

1. **确保只有一个实例** - GlobalUpgradeDialog 只应在主布局中渲染一次
2. **正确的 source 标识** - 每个调用点应使用唯一的 source 标识
3. **状态清理** - dialog 关闭时会自动清理状态
4. **事件追踪** - 保持了原有的分析事件追踪功能

## 测试验证

重构完成后，以下功能应正常工作：

- ✅ 左上角 Sidebar 的 "Upgrade Plan" 按钮
- ✅ API Management 区域的 "Upgrade Plan" 按钮
- ✅ 所有其他组件的升级按钮
- ✅ 不同的 defaultPlanType 配置
- ✅ 事件追踪和分析
