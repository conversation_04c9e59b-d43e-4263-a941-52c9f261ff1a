# Uppy 集成实施方案

## 📋 项目概述

本文档记录了在 Uniscribe Web 项目中引入 Uppy 来改善文件上传逻辑的完整实施方案。

### 目标功能

- [x] 前端引入 Uppy（不修改现有 UI）
- [x] 支持大文件分块上传
- [ ] 文件批量上传
- [ ] 支持公开访问的 Link（统计使用频率后决定是否对接各平台）
- [ ] 支持在线录音
- [ ] 支持其他来源的上传（Cloud Storage，需要 companion server，开发成本高，暂不考虑）

## 🔍 当前状态分析

### 现有上传架构

- **核心 Hook**: `useFileUpload` - 管理单文件上传状态和逻辑
- **UI 组件**:
  - `FileUploadArea` - 主要上传区域
  - `FileDisplay` - 文件信息显示
  - `UploadPrompt` - 上传提示界面
- **上传流程**: 文件选择 → FFmpeg 处理 → MD5 计算 → 获取预签名 URL → 上传到 Cloudflare → 完成上传
- **支持格式**: 音频/视频文件（MP3、MP4、WAV、FLAC 等）
- **现有功能**: 进度显示、取消上传、错误处理、文件预处理

### 后端 API 现状

- **上传端点**:
  - `/upload/generate-signed-url` - 生成预签名 URL
  - `/upload/complete` - 完成上传确认
- **批量操作**: 已支持批量删除、移动、导出
- **限制**: 无分块上传支持，当前是整文件上传到 Cloudflare

### 技术栈

- **前端**: Next.js + React + Axios
- **文件处理**: FFmpeg.wasm
- **存储**: Cloudflare R2 (S3 兼容)
- **状态管理**: Zustand

## ✅ 可行性评估

### 1. 前端引入 Uppy（不修改 UI）- **高度可行**

- 保持现有 UI 组件不变
- Uppy 作为底层上传引擎替换 axios
- 可以逐步迁移功能

### 2. 大文件分块上传 + 断点续传 - **需要后端配合**

- **挑战**: 当前后端只支持单次上传
- **解决方案**: 实现 multipart upload API 或使用 Cloudflare 的 multipart upload
- **技术方案**: 使用 Uppy 的 AwsS3Multipart 插件

### 3. 文件批量上传 - **高度可行**

- Uppy 天然支持批量上传
- 需要适配现有 UI 以支持多文件显示

### 4. 公开访问 Link - **可行，需要后端支持**

- 集成 Uppy 的 URL 插件
- 需要后端添加从 URL 下载文件的功能

### 5. 在线录音 - **高度可行**

- 使用 Uppy 的 Audio 插件
- 与现有录音功能集成

### 6. Cloud Storage 集成 - **复杂但可行**

- 需要 Companion server
- 需要对接各平台 OAuth
- 开发成本高，暂不优先考虑

## 🚧 实施计划

### Phase 1: 基础集成 (Week 1-2)

**目标**: 引入 Uppy 作为底层上传引擎，保持现有功能不变

#### 任务清单

- [x] 安装 Uppy 相关依赖包
- [x] 创建 `useUppyUpload` hook 替换 `useFileUpload`
- [x] 实现 Uppy 与现有预签名 URL 流程的集成
- [x] 保持现有 UI 组件不变，只替换底层逻辑
- [x] 测试单文件上传功能
- [x] 确保 FFmpeg 预处理、语言选择等功能正常工作

#### 技术要点

```javascript
// 基础 Uppy 配置
import Uppy from "@uppy/core";
import XHRUpload from "@uppy/xhr-upload";

const uppy = new Uppy({
  restrictions: {
    maxFileSize: 5 * 1024 * 1024 * 1024, // 5GB
    allowedFileTypes: SUPPORTED_FORMATS_ACCEPT,
  },
});
```

#### 验收标准

- [x] 单文件上传功能与原有功能完全一致
- [x] 进度显示正常
- [x] 错误处理正常
- [x] 取消上传功能正常

### Phase 2: 分块上传实现 (Week 3-4)

**目标**: 实现大文件分块上传和断点续传

#### 后端任务（Flask-RESTful + Cloudflare R2）

- [ ] 安装 boto3 依赖：`pip install boto3`
- [ ] 实现分块上传相关端点:
  - `POST /upload/multipart/create` - 创建分块上传
    ```python
    # 调用 R2 CreateMultipartUpload API
    # 返回 uploadId 和 transcriptionFileId
    # 存储上传会话信息到数据库
    ```
  - `GET /upload/multipart/{uploadId}/parts` - 列出已上传分块
    ```python
    # 调用 R2 ListParts API
    # 返回已完成的分块列表（支持断点续传）
    ```
  - `POST /upload/multipart/{uploadId}/part/{partNumber}/sign` - 获取分块预签名 URL
    ```python
    # 为指定分块生成预签名 URL
    # 支持 5MB-5GB 分块大小
    ```
  - `POST /upload/multipart/{uploadId}/complete` - 完成分块上传
    ```python
    # 调用 R2 CompleteMultipartUpload API
    # 合并所有分块，触发转录流程
    ```
  - `POST /upload/multipart/{uploadId}/abort` - 取消分块上传
    ```python
    # 清理未完成的分块上传
    ```

#### 前端任务

- [ ] 安装依赖：`npm install @uppy/aws-s3-multipart`
- [ ] 修改 `useUppyFileUpload.js` 添加分块上传支持
- [ ] 实现文件大小判断逻辑（>100MB 使用 multipart）
- [ ] 集成 AwsS3Multipart 插件配置
- [ ] 实现分块上传状态管理和进度显示
- [ ] 添加断点续传 UI 提示和恢复功能
- [ ] 测试大文件上传（>100MB, >1GB, >2GB）

#### 技术要点

**分块上传判断逻辑**：

```javascript
// 在 useUppyFileUpload.js 中添加
const MULTIPART_THRESHOLD = 100 * 1024 * 1024; // 100MB

const shouldUseMultipart = (file) => {
  return file.size > MULTIPART_THRESHOLD;
};
```

**AwsS3Multipart 插件配置**：

```javascript
import AwsS3Multipart from "@uppy/aws-s3-multipart";

// 只对大文件使用 multipart upload
if (shouldUseMultipart(file)) {
  uppy.use(AwsS3Multipart, {
    createMultipartUpload: async (file) => {
      // 调用后端 API: POST /upload/multipart/create
      const response = await uploadService.createMultipartUpload(file, md5Hash, duration, ...);
      return { uploadId: response.data.uploadId, key: response.data.key };
    },

    listParts: async (file, { uploadId }) => {
      // 调用后端 API: GET /upload/multipart/{uploadId}/parts
      const response = await uploadService.listMultipartParts(uploadId);
      return response.data.parts; // 支持断点续传
    },

    signPart: async (file, { uploadId, partNumber }) => {
      // 调用后端 API: POST /upload/multipart/{uploadId}/part/{partNumber}/sign
      const response = await uploadService.signMultipartPart(uploadId, partNumber);
      return { url: response.data.signedUrl };
    },

    completeMultipartUpload: async (file, { uploadId, parts }) => {
      // 调用后端 API: POST /upload/multipart/{uploadId}/complete
      const response = await uploadService.completeMultipartUpload(uploadId, parts);
      return response.data;
    },

    abortMultipartUpload: async (file, { uploadId }) => {
      // 调用后端 API: POST /upload/multipart/{uploadId}/abort
      await uploadService.abortMultipartUpload(uploadId);
    },

    // 分块配置
    chunkSize: 10 * 1024 * 1024, // 10MB 每块
    retryDelays: [0, 1000, 3000, 5000], // 重试延迟
  });
} else {
  // 小文件继续使用现有的 XHRUpload
  uppy.use(XHRUpload, { /* 现有配置 */ });
}
```

**后端 uploadService 扩展**：

```javascript
// 在 src/services/api/uploadService.js 中添加
export const uploadService = {
  // 现有方法...

  // Multipart Upload 相关方法
  createMultipartUpload: async (
    file,
    md5HashBase64,
    fileDuration,
    forceUpload,
    languageCode,
    subtitleEnabled,
    folderId
  ) => {
    const response = await axiosInstance.post("/upload/multipart/create", {
      filename: file.name.substring(0, file.name.lastIndexOf(".")),
      fileType: getFileExtension(file),
      fileSize: file.size,
      contentMd5Base64: md5HashBase64,
      duration: fileDuration,
      forceUpload,
      languageCode,
      transcriptionType: subtitleEnabled ? "subtitle" : "transcript",
      folderId: folderId && folderId !== "all" ? String(folderId) : undefined,
    });
    return response;
  },

  listMultipartParts: async (uploadId) => {
    const response = await axiosInstance.get(
      `/upload/multipart/${uploadId}/parts`
    );
    return response;
  },

  signMultipartPart: async (uploadId, partNumber) => {
    const response = await axiosInstance.post(
      `/upload/multipart/${uploadId}/part/${partNumber}/sign`
    );
    return response;
  },

  completeMultipartUpload: async (uploadId, parts) => {
    const response = await axiosInstance.post(
      `/upload/multipart/${uploadId}/complete`,
      { parts }
    );
    return response;
  },

  abortMultipartUpload: async (uploadId) => {
    const response = await axiosInstance.post(
      `/upload/multipart/${uploadId}/abort`
    );
    return response;
  },
};
```

#### 验收标准

- [ ] 支持 >2GB 文件上传（测试 2GB、3GB、5GB 文件）
- [ ] 网络中断后能够断点续传（自动检测已上传分块）
- [ ] 分块上传进度显示正确（显示总体进度和当前分块进度）
- [ ] 失败重试机制正常（分块级别的重试）
- [ ] 小文件（<100MB）继续使用单次上传，保持现有性能
- [ ] 上传取消功能正常（清理未完成的分块）

### Phase 3: 批量上传 (Week 5-6)

**目标**: 实现多文件批量上传功能

#### UI 改造任务

- [ ] 修改 `FileUploadArea` 支持多文件选择
- [ ] 创建批量文件列表组件
- [ ] 实现批量上传进度显示
- [ ] 添加单个文件的操作按钮（删除、重试等）

#### 功能任务

- [ ] 实现批量文件队列管理
- [ ] 支持并发上传控制
- [ ] 实现批量操作（全部开始、全部暂停、全部取消）
- [ ] 添加批量上传完成后的操作选项

#### 技术要点

```javascript
// 批量文件处理
uppy.addFiles([file1, file2, file3]);
uppy.upload();

// 监听批量上传进度
uppy.on("upload-progress", (file, progress) => {
  // 更新每个文件的进度
});
```

#### 验收标准

- [ ] 支持同时选择多个文件
- [ ] 每个文件独立显示进度
- [ ] 支持单个文件的暂停/恢复/取消
- [ ] 批量操作功能正常

### Phase 4: 高级功能 (Week 7-8)

**目标**: 实现在线录音和 URL 上传功能

#### 在线录音

- [ ] 集成 Uppy Audio 插件
- [ ] 实现录音 UI 集成
- [ ] 支持录音文件的直接上传

#### URL 上传

- [ ] 后端实现 URL 文件下载功能
- [ ] 前端添加 URL 输入界面
- [ ] 实现 URL 文件信息获取和验证

#### 技术要点

```javascript
import Audio from "@uppy/audio";
import Url from "@uppy/url";

// 录音功能
uppy.use(Audio, {
  showRecordingLength: true,
  showAudioSourceDropdown: true,
});

// URL 上传
uppy.use(Url, {
  companionUrl: "your-companion-server-url",
});
```

## 🔧 技术实现细节

### 依赖包安装

```bash
npm install @uppy/core @uppy/xhr-upload @uppy/aws-s3-multipart @uppy/audio @uppy/url @uppy/progress-bar
```

### 核心 Hook 重构

需要将现有的 `useFileUpload` 重构为 `useUppyUpload`，保持 API 兼容性。

### 状态管理

- 单文件状态 → 多文件状态管理
- 上传队列管理
- 进度状态同步

### 错误处理

- 网络错误重试
- 文件格式验证
- 大小限制检查
- 用户友好的错误提示

### 代码重构建议

当 Uppy 集成稳定后，可以进行以下代码优化：

#### 文件验证逻辑简化

- **移除 `validateFileSize` 函数**：Uppy 自带文件大小验证功能，无需手动验证
- **简化文件验证逻辑**：只保留 `isValidFileType` 函数，移除重复的验证代码
- **统一错误处理**：使用 Uppy 的错误事件系统，替代分散的错误处理逻辑

#### 具体实施步骤

1. 在 Uppy 配置中设置文件大小限制，移除手动 `validateFileSize` 调用
2. 保留 `isValidFileType` 用于文件类型检查
3. 将所有错误处理集中到 Uppy 的 `'restriction-failed'` 和 `'upload-error'` 事件中

#### 重构示例

```javascript
// Uppy 配置中统一设置限制
const uppy = new Uppy({
  restrictions: {
    maxFileSize: 5 * 1024 * 1024 * 1024, // 5GB
    allowedFileTypes: SUPPORTED_FORMATS_ACCEPT,
  },
});

// 统一错误处理
uppy.on("restriction-failed", (file, error) => {
  // 处理文件大小、类型等限制错误
  console.error("文件限制错误:", error.message);
});

uppy.on("upload-error", (file, error, response) => {
  // 处理上传错误
  console.error("上传错误:", error);
});

// 简化的文件验证（只保留必要的业务逻辑验证）
const validateFile = (file) => {
  return isValidFileType(file.type);
};
```

## ⚠️ 注意事项和风险

### 技术风险

1. **后端 API 兼容性**: 确保新的分块上传 API 与现有系统兼容
2. **状态管理复杂性**: 多文件上传的状态管理比单文件复杂
3. **UI 适配**: 批量上传需要重新设计部分 UI 组件
4. **性能影响**: 大文件和多文件上传对浏览器性能的影响

### 用户体验风险

1. **功能回归**: 确保现有功能在迁移过程中不受影响
2. **学习成本**: 新的批量上传界面可能需要用户适应
3. **兼容性**: 确保在不同浏览器和设备上的兼容性

### 缓解措施

1. **渐进式迁移**: 分阶段实施，每个阶段都确保功能稳定
2. **充分测试**: 每个阶段都进行全面测试
3. **回滚方案**: 保留原有代码，必要时可以快速回滚
4. **用户反馈**: 及时收集用户反馈并调整

## 📊 成功指标

### 技术指标

- [ ] 上传成功率 >99%
- [ ] 大文件（>1GB）上传成功率 >95%
- [ ] 断点续传成功率 >90%
- [ ] 批量上传并发性能满足需求

### 用户体验指标

- [ ] 用户上传操作流程保持简洁
- [ ] 错误提示清晰易懂
- [ ] 上传进度显示准确及时
- [ ] 新功能使用率逐步提升

## 📝 开发日志

### 更新记录

- **2025-06-28**: 创建初始实施方案文档
- **2025-06-28**: 完成 Phase 1 POC 开发
  - ✅ 安装 Uppy 相关依赖包 (`@uppy/core`, `@uppy/xhr-upload`, `@uppy/aws-s3`, `@uppy/audio`, `@uppy/url`, `@uppy/progress-bar`)
  - ✅ 创建 `useUppyUpload` hook，保持与原有 `useFileUpload` API 完全兼容
  - ✅ 实现基础 Uppy 集成，使用 XHRUpload 插件
  - ✅ 创建 POC 测试页面 (`/en/poc/uppy-test`)
  - ✅ 保持现有文件处理流程（FFmpeg 预处理、MD5 计算、预签名 URL）
  - ✅ 实现进度监听和错误处理
  - ✅ 测试页面可正常访问和使用
  - ✅ POC 测试页面地址：`http://localhost:3001/en/poc/uppy-test`
  - ✅ 验证了 Uppy 与现有后端 API 的兼容性
  - ✅ 确认了文件类型验证、大小验证、时长计算等功能正常

### 决策记录

- **UI 策略**: 决定保持现有 UI 设计，不使用 Uppy Dashboard
- **实施策略**: 采用渐进式迁移，分 4 个阶段实施
- **技术选型**: 选择 Uppy 作为上传引擎，使用 AwsS3 插件（替代已废弃的 AwsS3Multipart）
- **API 兼容性**: 新的 `useUppyUpload` hook 保持与原有 `useFileUpload` 完全相同的接口

---

**文档维护**: 本文档将随着项目进展持续更新，记录实际开发过程中的问题、解决方案和经验总结。
