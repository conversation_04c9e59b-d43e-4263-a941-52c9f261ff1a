"use client";

import React, { useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { trackEvent } from "@/lib/analytics";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import Pricing from "@/components/Home/Pricing";
import Faq from "@/components/Home/Faq";

const GlobalUpgradeDialog = () => {
  const t = useTranslations("common.upgradeDialog");
  const pathname = usePathname();

  const {
    isOpen,
    source,
    defaultPlanType,
    title,
    description,
    showFreeTier,
    closeDialog,
  } = useUpgradeDialogStore();

  useEffect(() => {
    if (isOpen) {
      trackEvent("upgrade_dialog_open", {
        source: source,
      });
    }
  }, [isOpen, pathname, source]);

  return (
    <Dialog open={isOpen} onOpenChange={closeDialog}>
      <DialogContent
        className="max-w-full h-screen max-h-screen p-0 md:p-6"
        style={{ width: "90%", maxWidth: "90%" }}
      >
        <div className="h-full overflow-y-auto">
          <div className="container mx-auto py-6 px-4">
            <Pricing
              showFreeTier={showFreeTier}
              title={title || t("title")}
              description={description}
              defaultPlanType={defaultPlanType}
              titleMarginTop=""
              containerClassName="w-full dialog-pricing"
              source={source}
            />
            <Faq />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GlobalUpgradeDialog;
