"use client";
import { useTranslations } from "next-intl";
import { useState, useRef, useEffect, useCallback } from "react";
import { Link } from "@/components/Common/Link";

import {
  calculateFileDuration,
  validateFileSize,
  isValidFileType,
} from "@/lib/fileUtils"; // 确保路径正确
import { isInvalidDuration } from "@/lib/utils";
import { transcriptionService } from "@/services/api/transcriptionService";
import { useAuthStore } from "@/stores/useAuthStore";
import supabase from "@/lib/supabaseClient";
import UploadPrompt from "@/components/Common/FileUploader/UploadPrompt";
import FileDisplay from "@/components/Common/FileUploader/FileDisplay";
import FileUploadStatus from "@/components/Common/FileUploader/FileUploadStatus";
import DeleteConfirmDialog from "@/components/Common/FileUploader/DeleteConfirmDialog";
import { trackAnonymousEvent, trackEvent } from "@/lib/analytics";
import { useRouter, usePathname } from "@/i18n/navigation";
import { SUPPORTED_FORMATS_ACCEPT } from "@/constants/file";
import { useFileUploadEngine } from "@/hooks/useFileUploadEngine";
import { getErrorMessage, reportUploadError } from "@/lib/utils";
import { storageService } from "@/services/storageService";
import GuestModeDialog from "@/components/Dashboard/GuestModeDialog";

const UploadTab = () => {
  const t = useTranslations("transcriptionBox.upload");
  const t_common = useTranslations("common");
  const maxFreeDuration = 120 * 60;

  // 1. Auth related hooks
  const { user } = useAuthStore();
  const router = useRouter();
  const pathname = usePathname();

  // 2. 使用统一的文件上传引擎Hook
  const {
    selectedFile,
    setSelectedFile,
    uploadProgress,
    uploadState,
    setUploadState,
    fileDuration,
    setFileDuration,
    transcriptionFileId,
    setTranscriptionFileId,
    showAlert,
    errorMessage,
    processingProgress,
    processing,

    // 新增：网速和剩余时间状态
    uploadSpeed,
    estimatedTimeRemaining,
    isMultipartUpload,

    uploadFile,
    cancelUpload,
    setShowAlert,
    setErrorMessage,
  } = useFileUploadEngine();

  // 4. Other state hooks
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [errorAction, setErrorAction] = useState("Sign in");
  const [errorActionHandler, setErrorActionHandler] = useState(
    () => () => router.push("/auth/signin")
  );
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitDialogSource, setLimitDialogSource] =
    useState("file_upload_limit");

  // 5. Refs
  const fileInputRef = useRef(null);
  const fileDisplayRef = useRef(null); // 添加FileDisplay的ref
  const abortControllerRef = useRef(null);

  // 6. Derived state
  const isLoggedIn = !!user && !user.isAnonymous;

  // 1. 添加共同的文件处理逻辑
  const handleFileProcess = useCallback(
    async (file) => {
      abortControllerRef.current = new AbortController();

      // 清除之前的错误状态
      setShowAlert(false);
      setErrorMessage("");

      try {
        // 检查文件类型
        const isValid = isValidFileType(file);
        if (!isValid) {
          // 打点记录无效文件类型
          trackEvent("invalid_file_type_detected", {
            fileType: file.type || "unknown",
          });

          setErrorMessage(
            t("error.unsupportedFileType", { fileType: file.type || "unknown" })
          );
          setShowAlert(true);
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
          return;
        }

        // 检查文件大小 - 对所有引擎都执行检查
        const sizeValidation = validateFileSize(file, t_common);

        // 对于所有引擎，如果文件超过 5GB，直接阻止上传
        if (!sizeValidation.isValid) {
          // For files exceeding 5GB, show error with tutorial link
          if (sizeValidation.showTutorialLink) {
            const tutorialMessage = (
              <div className="text-center">
                <div className="mb-2 text-red-600">
                  {sizeValidation.message}{" "}
                  {t_common("fileUploadStatus.tutorial.errorExplanation")}
                </div>
                <Link
                  href="/blog/extract-audio-from-video-vlc-guide?utm_source=upload_tab"
                  target="_blank"
                  className="text-custom-bg hover:text-custom-bg/80 underline"
                >
                  {t_common("fileUploadStatus.tutorial.viewTutorial")}
                </Link>
              </div>
            );
            setErrorMessage(tutorialMessage);
          } else {
            setErrorMessage(sizeValidation.message);
          }
          setShowAlert(true);
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
          return;
        }

        const duration = await calculateFileDuration(file);
        setSelectedFile(file);
        setFileDuration(duration);
        setUploadState("preparing");

        if (abortControllerRef.current.signal.aborted) {
          throw new Error("Operation cancelled");
        }

        // 匿名用户登录逻辑
        if (!isLoggedIn) {
          const {
            data: { session },
          } = await supabase.auth.getSession();

          if (!session) {
            const { data, error } = await supabase.auth.signInAnonymously();
            if (error) throw error;
          }
        }

        // 时长检查和登录提示
        // 只有当duration是有效值时才进行比较
        if (
          !isInvalidDuration(duration) &&
          duration > maxFreeDuration &&
          !isLoggedIn
        ) {
          console.log("Duration exceeds free limit:", duration);
          trackAnonymousEvent("login_prompted", {
            path: pathname,
          });

          setErrorMessage(t("error.loginRequired"));
          setErrorAction(t("error.action"));
          setErrorActionHandler(() => () => router.push("/auth/signin"));
          setShowAlert(true);
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
          return;
        }

        // 使用更新后的Hook的上传功能 - 不再需要传递语言和字幕参数
        const fileId = await uploadFile(file, duration);

        // 上传成功处理
        if (fileId) {
          setTranscriptionFileId(fileId);
          handleUploadSuccess(file.type);

          // 通知FileDisplay文件处理完成，应用待处理的Speaker Recognition设置
          if (fileDisplayRef.current?.onProcessingComplete) {
            fileDisplayRef.current.onProcessingComplete(fileId);
          }

          // 匿名用户文件存储
          if (!isLoggedIn) {
            localStorage.setItem(
              "anonymousUpload",
              JSON.stringify({
                fileId: fileId,
                fileName: file.name,
                timestamp: new Date().toISOString(),
                fromPage: pathname,
                type: "upload", // 明确标记为upload类型
              })
            );
          }
        }
      } catch (error) {
        if (error.message === "Operation cancelled") {
          return;
        }

        console.error("Error handling file upload:", error);

        // 错误跟踪
        trackAnonymousEvent("upload_error", {
          fileType: file.type,
          errorMessage: error.message || String(error),
          path: pathname,
          userId: user?.id,
        });
        reportUploadError({
          error,
          fileType: file.type,
          fileName: file.name,
          fileSize: file.size,
          userId: user?.id,
          userEmail: user?.email,
        });
      }
    },
    [
      isLoggedIn,
      maxFreeDuration,
      pathname,
      t,
      uploadFile,
      router,
      user?.id,
      setShowAlert,
      setErrorMessage,
    ]
  );

  // 2. 简化后的 handleFileChange
  const handleFileChange = async (e) => {
    e.stopPropagation();
    const file = e.target.files[0];
    if (!file) return;

    trackAnonymousEvent("file_selected", {
      fileType: file.type,
      path: pathname,
    });

    await handleFileProcess(file);
  };

  // 3. 简化后的 handleDrop
  const handleDrop = useCallback(
    async (e) => {
      e.preventDefault();
      // 添加检查：如果已有选中文件，则不处理拖放的新文件
      if (selectedFile !== null) return;

      const file = e.dataTransfer.files[0];
      if (!file) return;

      trackAnonymousEvent("file_dropped", {
        fileType: file.type,
        path: pathname,
      });

      await handleFileProcess(file);
    },
    [selectedFile, pathname, handleFileProcess] // 添加selectedFile到依赖数组
  );

  const handleRemoveFile = () => {
    setShowDeleteConfirm(true);
  };

  const confirmRemoveFile = () => {
    // 跟踪文件删除 - 特有的
    trackAnonymousEvent("file_deleted", {
      fileType: selectedFile?.type,
      uploadState: uploadState,
      path: pathname,
    });

    // 使用Hook取消上传
    cancelUpload();

    // 文件输入重置
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }

    setShowDeleteConfirm(false);

    // 删除已上传的文件（如果存在）- 特有的
    if (transcriptionFileId) {
      transcriptionService.deleteTranscription(transcriptionFileId);
      setTranscriptionFileId(null);
      localStorage.removeItem("anonymousUpload");
    }
  };

  const handleUploadSuccess = (fileType) => {
    // 跟踪上传完成 - 特有的
    trackAnonymousEvent("upload_completed", {
      path: pathname,
    });
  };

  // 检查匿名文件 - 仅恢复通过Upload Tab上传的文件
  useEffect(() => {
    const checkAnonymousFile = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session || (session && session.user && !session.user.is_anonymous))
        return;

      try {
        // 只检查localStorage中存储的Upload Tab文件
        const anonymousUpload = localStorage.getItem("anonymousUpload");
        if (!anonymousUpload) return;

        const uploadData = JSON.parse(anonymousUpload);

        // 只处理非YouTube类型的转录（Upload File类型）
        if (uploadData.type === "youtube") {
          return; // YouTube转录由TranscriptionBox处理
        }

        // 验证文件是否仍然存在
        const { data } = await transcriptionService.getTranscriptionById(
          uploadData.fileId
        );
        if (!data) {
          // 文件不存在，清除localStorage
          localStorage.removeItem("anonymousUpload");
          return;
        }

        const { id, filename, fileSize, duration, status } = data;
        setTranscriptionFileId(id);
        setSelectedFile({
          name: filename,
          size: fileSize,
          status: status,
          id: id,
        });
        setFileDuration(duration);
        setUploadState("success");
      } catch (error) {
        console.error("Failed to fetch anonymous file:", error);
        // 出错时清除localStorage
        localStorage.removeItem("anonymousUpload");
      }
    };

    checkAnonymousFile();
  }, [isLoggedIn]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, []);

  const handleTranscribeClick = async () => {
    // 如果已经在转录中，防止重复点击
    if (isTranscribing) return;

    // 检查匿名用户文件转录次数限制
    if (!isLoggedIn && !storageService.canTranscribeFile()) {
      setShowLimitDialog(true);
      setLimitDialogSource("file_upload_limit");
      return;
    }

    // 检查匿名用户30分钟限制
    if (!isLoggedIn && !isInvalidDuration(fileDuration)) {
      const durationInMinutes = Math.ceil(fileDuration / 60); // 将秒转换为分钟，向上取整

      // 检查是否已经超过30分钟限制
      if (storageService.hasExceededMinutesLimit()) {
        setShowLimitDialog(true);
        setLimitDialogSource("minutes_limited");
        return;
      }

      // 检查新文件时长加上已有时长是否会超过30分钟
      if (!storageService.canTranscribeWithDuration(durationInMinutes)) {
        setShowLimitDialog(true);
        setLimitDialogSource("minutes_limited");
        return;
      }
    }

    // 设置转录状态为加载中
    setIsTranscribing(true);

    trackAnonymousEvent("transcribe_click", {
      path: pathname,
    });

    try {
      await transcriptionService.doTranscription(transcriptionFileId);

      // 转录成功后，增加匿名用户文件转录计数和分钟数
      if (!isLoggedIn && !isInvalidDuration(fileDuration)) {
        const durationInMinutes = Math.ceil(fileDuration / 60);
        storageService.incrementAnonymousFileCount(durationInMinutes);
      }

      router.push(`/transcriptions/${transcriptionFileId}`);
    } catch (error) {
      setErrorMessage(t("error.transcriptionFailed"));
      setShowAlert(true);
      // 出错时重置转录状态
      setIsTranscribing(false);
    }
  };

  // 1. 首先修改handleUploadClick函数来阻止事件冒泡
  const handleUploadClick = (e) => {
    if (e) {
      e.stopPropagation(); // 阻止事件冒泡
    }
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 2. 修改handleGotoDashboard函数来阻止事件冒泡
  const handleGotoDashboard = (e) => {
    if (e) {
      e.stopPropagation(); // 阻止事件冒泡
    }
    router.push("/dashboard");
  };

  // 1. 修改处理函数，统一处理逻辑
  const handleAreaClick = (e) => {
    // 阻止事件冒泡
    e.stopPropagation();

    if (selectedFile !== null) return;

    if (isLoggedIn) {
      handleGotoDashboard();
    } else {
      handleUploadClick();
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto pb-4 px-4 md:pb-6 md:px-6 md:pt-4">
      <div className="space-y-4">
        <p className="text-sm md:text-base text-center text-muted-foreground">
          {t("description")}
        </p>

        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          accept={SUPPORTED_FORMATS_ACCEPT}
          onChange={handleFileChange}
        />

        {/* File Upload Area */}
        <div
          className={`border-2 border-dashed border-custom-bg/60 rounded-lg p-8 ${
            selectedFile === null ? "cursor-pointer" : ""
          } transition-colors duration-200 hover:border-custom-bg`}
          onClick={handleAreaClick}
          onDrop={handleDrop}
          onDragOver={(e) => e.preventDefault()}
        >
          {selectedFile === null ? (
            <UploadPrompt
              isLoggedIn={isLoggedIn}
              onButtonClick={handleAreaClick}
              variant="home"
              onGotoDashboard={handleGotoDashboard}
            />
          ) : (
            <FileDisplay
              ref={fileDisplayRef}
              file={selectedFile}
              fileDuration={fileDuration}
              onRemove={handleRemoveFile}
              transcriptionFileId={transcriptionFileId}
            />
          )}
        </div>

        <FileUploadStatus
          uploadState={uploadState}
          uploadProgress={uploadProgress}
          processingProgress={processingProgress}
          processing={processing}
          onTranscribeClick={handleTranscribeClick}
          showAlert={showAlert}
          errorMessage={errorMessage}
          errorAction={t("error.action")}
          onErrorAction={errorActionHandler}
          selectedFile={selectedFile}
          isTranscribing={isTranscribing}
          uploadSpeed={uploadSpeed}
          estimatedTimeRemaining={estimatedTimeRemaining}
          isMultipartUpload={isMultipartUpload}
        />
      </div>

      <DeleteConfirmDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        onConfirm={confirmRemoveFile}
      />

      {/* 转录次数限制弹框 */}
      <GuestModeDialog
        isOpen={showLimitDialog}
        onClose={() => setShowLimitDialog(false)}
        source={limitDialogSource}
        remainingMinutes={storageService.getAnonymousRemainingMinutes()}
        requiredMinutes={Math.ceil(fileDuration / 60)}
      />
    </div>
  );
};

export default UploadTab;
