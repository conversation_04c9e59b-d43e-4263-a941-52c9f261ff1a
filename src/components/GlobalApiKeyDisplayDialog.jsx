"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { Copy, Check, AlertTriangle } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useApiKeyDisplayDialogStore } from "@/stores/useApiKeyDisplayDialogStore";

export default function GlobalApiKeyDisplayDialog() {
  const t = useTranslations("settings.apiKeyManagement");
  const { isOpen, apiKey, isReset, closeDialog } =
    useApiKeyDisplayDialogStore();

  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    if (!apiKey?.apiKey) return;

    try {
      await navigator.clipboard.writeText(apiKey.apiKey);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy to clipboard:", error);
    }
  };

  // 监听 dialog 状态，确保关闭时强制清理 DOM 状态
  useEffect(() => {
    if (!isOpen) {
      // 强制恢复页面的交互性，解决按钮不可点击的问题
      setTimeout(() => {
        // 移除任何可能残留的 focus trap 或 inert 属性
        document.body.style.pointerEvents = "";
        document.documentElement.style.pointerEvents = "";

        // 确保没有残留的 modal 相关属性
        const bodyClassList = document.body.classList;
        bodyClassList.remove("overflow-hidden");

        // 移除任何可能的 inert 属性
        document.querySelectorAll("[inert]").forEach((el) => {
          el.removeAttribute("inert");
        });
      }, 100);
    }
  }, [isOpen]);

  const handleClose = () => {
    setCopied(false);
    closeDialog();
  };

  if (!apiKey) return null;

  return (
    <Dialog open={isOpen} onOpenChange={closeDialog}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isReset ? t("keyResetDialog.title") : t("keyCreatedDialog.title")}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <p className="font-medium">{t("keyWarning")}</p>
                <p className="text-sm">{t("keyStorageWarning")}</p>
              </div>
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <Label htmlFor="api-key">{t("fullKey")}</Label>
            <div className="flex gap-2">
              <Input
                id="api-key"
                value={apiKey.apiKey || ""}
                readOnly
                className="font-mono text-sm"
                onClick={(e) => e.target.select()}
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={copyToClipboard}
                className="flex-shrink-0"
              >
                {copied ? (
                  <Check className="h-4 w-4 text-green-600" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <Label className="text-xs text-muted-foreground">
                {t("keyName")}
              </Label>
              <p className="font-medium">{apiKey.name}</p>
            </div>
            <div>
              <Label className="text-xs text-muted-foreground">
                {t("createdAt")}
              </Label>
              <p className="font-medium">
                {apiKey.createdTime
                  ? new Date(apiKey.createdTime).toLocaleDateString()
                  : "-"}
              </p>
            </div>
            <div>
              <Label className="text-xs text-muted-foreground">
                {t("expiration")}
              </Label>
              <p className="font-medium">
                {apiKey.expiresAt
                  ? new Date(apiKey.expiresAt).toLocaleDateString()
                  : t("noExpiration")}
              </p>
            </div>
            <div>
              <Label className="text-xs text-muted-foreground">
                Rate Limit
              </Label>
              <p className="font-medium">
                {apiKey.rateLimitPerMinute || 60}/min,{" "}
                {apiKey.rateLimitPerDay || 1000}/day
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            onClick={copyToClipboard}
            className="bg-custom-bg hover:bg-custom-bg-600 w-full sm:w-auto"
          >
            {copied ? (
              <>
                <Check className="h-4 w-4 mr-2" />
                {t("copied")}
              </>
            ) : (
              <>
                <Copy className="h-4 w-4 mr-2" />
                {isReset
                  ? t("keyResetDialog.copyButton")
                  : t("keyCreatedDialog.copyButton")}
              </>
            )}
          </Button>
          <Button
            variant="outline"
            onClick={handleClose}
            className="w-full sm:w-auto"
          >
            {isReset ? t("keyResetDialog.close") : t("keyCreatedDialog.close")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
