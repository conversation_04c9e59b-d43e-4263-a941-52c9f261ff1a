"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import {
  Key,
  Plus,
  Copy,
  Edit,
  RotateCcw,
  Trash2,
  AlertTriangle,
  ExternalLink,
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { apiKeyService } from "@/services/api/apiKeyService";
import CreateApiKeyDialog from "./CreateApiKeyDialog";
import RenameApiKeyDialog from "./RenameApiKeyDialog";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import { useApiKeyDisplayDialogStore } from "@/stores/useApiKeyDisplayDialogStore";
import { safeOpenUrl } from "@/lib/browserUtils";

export default function ApiManagementSection({ user }) {
  const t = useTranslations("settings.apiKeyManagement");

  // State management
  const [apiKeys, setApiKeys] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const { openDialog } = useUpgradeDialogStore();
  const { openDialog: openApiKeyDialog } = useApiKeyDisplayDialogStore();

  // Selected key for operations
  const [selectedKey, setSelectedKey] = useState(null);

  // Check if user has API access (subscription or LTD plan)
  const hasApiAccess = user?.hasApiAccess || false;

  // Load API keys on component mount
  useEffect(() => {
    if (hasApiAccess) {
      loadApiKeys();
    } else {
      setLoading(false);
    }
  }, [hasApiAccess]);

  const loadApiKeys = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiKeyService.getApiKeys();

      if (response.status === 200 && response.data?.success) {
        setApiKeys(response.data.data.apiKeys || []);
      } else {
        throw new Error("Failed to load API keys");
      }
    } catch (error) {
      console.error("Failed to load API keys:", error);
      setError(error?.data?.error?.message || t("errorMessages.loadFailed"));
      // Handle specific error cases
      if (error?.status === 403) {
        setError(t("errorMessages.accessDenied"));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCreateKey = async (keyData) => {
    try {
      const response = await apiKeyService.createApiKey(keyData);

      if (response.status === 200 && response.data?.success) {
        const newKey = response.data.data;
        setApiKeys((prev) => [...prev, newKey]);
        openApiKeyDialog(newKey, false);
        toast.success(t("successMessages.keyCreated"));
      } else {
        throw new Error("Failed to create API key");
      }
    } catch (error) {
      console.error("Failed to create API key:", error);
      const errorMessage =
        error?.data?.error?.message || t("errorMessages.createFailed");
      toast.error(errorMessage);
      throw error;
    }
  };

  const handleRenameKey = async (keyId, newName) => {
    try {
      const response = await apiKeyService.updateApiKey(keyId, {
        name: newName,
      });

      if (response.status === 200 && response.data?.success) {
        const updatedKey = response.data.data;
        setApiKeys((prev) =>
          prev.map((key) => (key.id === keyId ? updatedKey : key))
        );
        toast.success(t("successMessages.keyUpdated"));
      } else {
        throw new Error("Failed to update API key");
      }
    } catch (error) {
      console.error("Failed to update API key:", error);
      const errorMessage =
        error?.data?.error?.message || t("errorMessages.updateFailed");
      toast.error(errorMessage);
      throw error;
    }
  };

  const handleResetKey = async () => {
    if (!selectedKey) return;

    try {
      const response = await apiKeyService.resetApiKey(selectedKey.id);

      if (response.status === 200 && response.data?.success) {
        const resetKey = response.data.data;
        setApiKeys((prev) =>
          prev.map((key) => (key.id === selectedKey.id ? resetKey : key))
        );
        openApiKeyDialog(resetKey, true);
        toast.success(t("successMessages.keyReset"));
      } else {
        throw new Error("Failed to reset API key");
      }
    } catch (error) {
      console.error("Failed to reset API key:", error);
      const errorMessage =
        error?.data?.error?.message || t("errorMessages.resetFailed");
      toast.error(errorMessage);
    } finally {
      setResetDialogOpen(false);
      setSelectedKey(null);
    }
  };

  const handleDeleteKey = async () => {
    if (!selectedKey) return;

    try {
      const response = await apiKeyService.deleteApiKey(selectedKey.id);

      if (response.status === 200 && response.data?.success) {
        setApiKeys((prev) => prev.filter((key) => key.id !== selectedKey.id));
        toast.success(t("successMessages.keyDeleted"));
      } else {
        throw new Error("Failed to delete API key");
      }
    } catch (error) {
      console.error("Failed to delete API key:", error);
      const errorMessage =
        error?.data?.error?.message || t("errorMessages.deleteFailed");
      toast.error(errorMessage);
    } finally {
      setDeleteDialogOpen(false);
      setSelectedKey(null);
    }
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(t("copied"));
    } catch (error) {
      toast.error("copy failed");
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return t("neverUsed");
    return new Date(dateString).toLocaleDateString();
  };

  const isKeyExpired = (expiresAt) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  // Render access denied state
  if (!hasApiAccess) {
    return (
      <Card className="overflow-hidden" id="api">
        <CardHeader className="bg-muted/50">
          <div className="flex items-center gap-2">
            <CardTitle className="text-xl">{t("title")}</CardTitle>
            <Badge
              variant="secondary"
              className="text-xs bg-orange-100 text-orange-700 border-orange-200"
            >
              Beta
            </Badge>
          </div>
          <CardDescription>{t("description")}</CardDescription>
          <div className="mt-2">
            <button
              onClick={() => safeOpenUrl("/docs")}
              className="inline-flex items-center text-sm text-custom-500 hover:text-custom-600 underline cursor-pointer bg-transparent border-none p-0"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              {t("viewApiDocs") || "View API Documentation"}
            </button>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mb-4" />
            <h3 className="text-lg font-medium mb-2">
              {t("requiresSubscription")}
            </h3>
            <p className="text-muted-foreground mb-4">{t("upgradePrompt")}</p>
            <div className="space-y-2">
              <Button
                className="bg-custom-bg hover:bg-custom-bg-600 w-full"
                onClick={() => {
                  openDialog({
                    source: "api_management",
                    defaultPlanType: "yearly",
                  });
                }}
              >
                {t("upgradePlan") || "Upgrade Plan"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="overflow-hidden" id="api">
        <CardHeader className="bg-muted/50">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-2">
                <CardTitle className="text-xl">{t("title")}</CardTitle>
                <Badge
                  variant="secondary"
                  className="text-xs bg-orange-100 text-orange-700 border-orange-200"
                >
                  Beta
                </Badge>
              </div>
              <CardDescription>{t("description")}</CardDescription>
              <div className="mt-2">
                <button
                  onClick={() => safeOpenUrl("/docs")}
                  className="inline-flex items-center text-sm text-custom-500 hover:text-custom-600 underline cursor-pointer bg-transparent border-none p-0"
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  {t("viewApiDocs") || "View API Documentation"}
                </button>
              </div>
            </div>
            <Button
              onClick={() => setCreateDialogOpen(true)}
              disabled={loading || apiKeys.length >= 5}
              className="bg-custom-bg hover:bg-custom-bg-600"
            >
              <Plus className="h-4 w-4 mr-2" />
              {t("createKey")}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-custom-bg"></div>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <AlertTriangle className="h-8 w-8 text-red-500 mb-2" />
              <p className="text-red-600">{error}</p>
              <Button variant="outline" onClick={loadApiKeys} className="mt-4">
                Try Again
              </Button>
            </div>
          ) : apiKeys.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Key className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">{t("noKeys")}</h3>
              <p className="text-muted-foreground mb-4">
                {t("noKeysDescription")}
              </p>
              <Button
                onClick={() => setCreateDialogOpen(true)}
                className="bg-custom-bg hover:bg-custom-bg-600"
              >
                <Plus className="h-4 w-4 mr-2" />
                {t("createFirstKey")}
              </Button>
            </div>
          ) : (
            <>
              {apiKeys.length >= 5 && (
                <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div className="flex items-center">
                    <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-yellow-800">
                        {t("maxKeysReached")}
                      </p>
                      <p className="text-sm text-yellow-700">
                        {t("maxKeysDescription")}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                {apiKeys.map((key) => (
                  <Card key={key.id} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-medium text-lg">{key.name}</h3>
                          {isKeyExpired(key.expiresAt) ? (
                            <Badge variant="destructive">{t("expired")}</Badge>
                          ) : (
                            <Badge variant="secondary">{t("active")}</Badge>
                          )}
                        </div>

                        <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                          <span>Created: {formatDate(key.createdTime)}</span>
                          <span>{formatDate(key.lastUsedTime)}</span>
                        </div>

                        <div className="flex items-center gap-2">
                          <code className="text-sm bg-muted px-3 py-2 rounded font-mono">
                            {key.keyPreview}
                          </code>
                        </div>
                      </div>

                      <div className="flex items-center gap-1 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedKey(key);
                            setRenameDialogOpen(true);
                          }}
                          title={t("rename")}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedKey(key);
                            setResetDialogOpen(true);
                          }}
                          title={t("reset")}
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedKey(key);
                            setDeleteDialogOpen(true);
                          }}
                          title={t("delete")}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Create API Key Dialog */}
      <CreateApiKeyDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onCreateKey={handleCreateKey}
      />

      {/* Rename API Key Dialog */}
      <RenameApiKeyDialog
        open={renameDialogOpen}
        onOpenChange={setRenameDialogOpen}
        apiKey={selectedKey}
        onRenameKey={handleRenameKey}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("confirmDelete")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("confirmDeleteDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("createDialog.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteKey}
              className="bg-red-600 hover:bg-red-700"
            >
              {t("delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Reset Confirmation Dialog */}
      <AlertDialog open={resetDialogOpen} onOpenChange={setResetDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("confirmReset")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("confirmResetDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("createDialog.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleResetKey}
              className="bg-custom-bg hover:bg-custom-bg-600"
            >
              {t("reset")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
