"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import {
  ArrowLeft,
  Settings,
  Bell,
  AlertTriangle,
  User,
  Key,
  BarChart3,
} from "lucide-react";
import { useRouter } from "@/i18n/navigation";

import { Button } from "@/components/ui/button";

import { useAuthStore } from "@/stores/useAuthStore";
import ProfileSection from "./ProfileSection";
import PreferencesSection from "./PreferencesSection";
import UsageSection from "./UsageSection";
import ApiManagementSection from "./ApiManagementSection";
import NotificationsSection from "./NotificationsSection";
import DangerZoneSection from "./DangerZoneSection";
import GlobalApiKeyDisplayDialog from "@/components/GlobalApiKeyDisplayDialog";

export default function SettingsPage() {
  const { user } = useAuthStore();
  const t = useTranslations("settings");
  const router = useRouter();

  // 使用 MediaQuery 检测是否为桌面端
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // 当前活动的导航项
  const [activeSection, setActiveSection] = useState("profile");

  // 滚动到指定部分（仅桌面端）
  const scrollToSection = (sectionId) => {
    // 只在桌面端执行
    if (!isDesktop) return;

    setActiveSection(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      // 使用平滑滚动效果
      element.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  // 监听滚动事件，更新当前活动的导航项（仅桌面端）
  useEffect(() => {
    // 只在桌面端添加滚动监听
    if (!isDesktop) return;

    const handleScroll = () => {
      const sections = [
        "profile",
        "usage",
        "preferences",
        "api",
        "notifications",
        "danger",
      ];

      // 找到当前在视口中的部分
      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          // 如果部分顶部在视口中或刚好在视口上方不远处
          if (rect.top <= 100 && rect.bottom >= 100) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    // 初始检查
    handleScroll();

    // 添加事件监听器
    window.addEventListener("scroll", handleScroll);

    // 清理事件监听器
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [isDesktop]);

  return (
    <div className="flex w-full flex-col">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/dashboard")}
            className="mr-2"
            aria-label={t("page.backButton")}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              {t("page.title")}
            </h1>
            <p className="text-muted-foreground">{t("page.subtitle")}</p>
          </div>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-8">
        {/* Left Navigation Menu - Only visible on desktop */}
        <div className="hidden md:block md:w-64 flex-shrink-0">
          <div className="sticky top-4 space-y-1">
            <Button
              variant={activeSection === "profile" ? "default" : "ghost"}
              className={`w-full justify-start ${
                activeSection === "profile"
                  ? "bg-custom-bg hover:bg-custom-bg-600"
                  : ""
              }`}
              onClick={() => scrollToSection("profile")}
            >
              <User className="mr-2 h-4 w-4" />
              {t("navigation.profile")}
            </Button>

            <Button
              variant={activeSection === "usage" ? "default" : "ghost"}
              className={`w-full justify-start ${
                activeSection === "usage"
                  ? "bg-custom-bg hover:bg-custom-bg-600"
                  : ""
              }`}
              onClick={() => scrollToSection("usage")}
            >
              <BarChart3 className="mr-2 h-4 w-4" />
              {t("navigation.usage")}
            </Button>

            <Button
              variant={activeSection === "preferences" ? "default" : "ghost"}
              className={`w-full justify-start ${
                activeSection === "preferences"
                  ? "bg-custom-bg hover:bg-custom-bg-600"
                  : ""
              }`}
              onClick={() => scrollToSection("preferences")}
            >
              <Settings className="mr-2 h-4 w-4" />
              {t("navigation.preferences")}
            </Button>

            <Button
              variant={activeSection === "api" ? "default" : "ghost"}
              className={`w-full justify-start ${
                activeSection === "api"
                  ? "bg-custom-bg hover:bg-custom-bg-600"
                  : ""
              }`}
              onClick={() => scrollToSection("api")}
            >
              <Key className="mr-2 h-4 w-4" />
              {t("navigation.apiKeyManagement")}
            </Button>
            <Button
              variant={activeSection === "notifications" ? "default" : "ghost"}
              className={`w-full justify-start ${
                activeSection === "notifications"
                  ? "bg-custom-bg hover:bg-custom-bg-600"
                  : ""
              }`}
              onClick={() => scrollToSection("notifications")}
            >
              <Bell className="mr-2 h-4 w-4" />
              {t("navigation.notifications")}
            </Button>
            <Button
              variant={activeSection === "danger" ? "default" : "ghost"}
              className={`w-full justify-start ${
                activeSection === "danger"
                  ? "bg-red-500 hover:bg-red-600 text-white"
                  : "text-red-500 hover:text-red-600"
              }`}
              onClick={() => scrollToSection("danger")}
            >
              <AlertTriangle className="mr-2 h-4 w-4" />
              {t("navigation.dangerZone")}
            </Button>
          </div>
        </div>

        {/* Right Content Area */}
        <div className="w-full md:flex-1 space-y-8">
          <ProfileSection user={user} />
          <UsageSection user={user} />
          <PreferencesSection user={user} />
          <ApiManagementSection user={user} />
          <NotificationsSection user={user} />
          <DangerZoneSection />
        </div>
      </div>

      {/* API Key Display Dialog - Only used in Settings */}
      <GlobalApiKeyDisplayDialog />
    </div>
  );
}
