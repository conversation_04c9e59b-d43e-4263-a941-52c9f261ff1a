"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function RenameApiKeyDialog({
  open,
  onOpenChange,
  apiKey,
  onRenameKey,
}) {
  const t = useTranslations("settings.apiKeyManagement");

  const [name, setName] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Update name when api<PERSON><PERSON> changes
  useEffect(() => {
    if (apiKey) {
      setName(apiKey.name || "");
    }
  }, [apiKey]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!apiKey) return;

    // Validate name
    if (!name.trim()) {
      setError(t("errorMessages.invalidName"));
      return;
    }

    if (name.length > 100) {
      setError(t("errorMessages.invalidName"));
      return;
    }

    if (name.trim() === apiKey.name) {
      // No change, just close dialog
      onOpenChange(false);
      return;
    }

    try {
      setLoading(true);
      setError("");

      await onRenameKey(apiKey.id, name.trim());

      // Close dialog on success
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setName(apiKey?.name || "");
    setError("");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("renameDialog.title")}</DialogTitle>
          <DialogDescription>{t("renameDialog.description")}</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="rename-name">{t("renameDialog.nameLabel")}</Label>
              <Input
                id="rename-name"
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                  setError("");
                }}
                className={error ? "border-red-500" : ""}
                maxLength={100}
                autoFocus
              />
              {error && <p className="text-sm text-red-600">{error}</p>}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
            >
              {t("renameDialog.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-custom-bg hover:bg-custom-bg-600"
            >
              {loading ? t("renameDialog.saving") : t("renameDialog.save")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
