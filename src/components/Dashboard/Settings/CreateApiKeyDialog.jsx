"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function CreateApiKeyDialog({
  open,
  onOpenChange,
  onCreateKey,
}) {
  const t = useTranslations("settings.apiKeyManagement");

  const [formData, setFormData] = useState({
    name: "",
    expiresDays: "",
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = t("errorMessages.invalidName");
    } else if (formData.name.length > 100) {
      newErrors.name = t("errorMessages.invalidName");
    }

    if (formData.expiresDays) {
      const days = parseInt(formData.expiresDays);
      if (isNaN(days) || days < 1 || days > 3650) {
        newErrors.expiresDays = t("errorMessages.invalidExpiration");
      }
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      setLoading(true);
      setErrors({});

      const keyData = {
        name: formData.name.trim(),
      };

      if (formData.expiresDays) {
        keyData.expiresDays = parseInt(formData.expiresDays);
      }

      await onCreateKey(keyData);

      // Reset form and close dialog
      setFormData({ name: "", expiresDays: "" });
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({ name: "", expiresDays: "" });
    setErrors({});
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("createDialog.title")}</DialogTitle>
          <DialogDescription>
            Create a new API key to access the UniScribe API.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">{t("createDialog.nameLabel")}</Label>
              <Input
                id="name"
                placeholder={t("createDialog.namePlaceholder")}
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                className={errors.name ? "border-red-500" : ""}
                maxLength={100}
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="expiresDays">
                {t("createDialog.expirationLabel")}
              </Label>
              <Input
                id="expiresDays"
                type="number"
                placeholder={t("createDialog.expirationPlaceholder")}
                value={formData.expiresDays}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    expiresDays: e.target.value,
                  }))
                }
                className={errors.expiresDays ? "border-red-500" : ""}
                min="1"
                max="3650"
              />
              <p className="text-xs text-muted-foreground">
                {t("createDialog.expirationHelp")}
              </p>
              {errors.expiresDays && (
                <p className="text-sm text-red-600">{errors.expiresDays}</p>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
            >
              {t("createDialog.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-custom-bg hover:bg-custom-bg-600"
            >
              {loading ? t("createDialog.creating") : t("createDialog.create")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
