/**
 * Shared UsageSummary component for displaying usage overview
 */

import { useTranslations } from "next-intl";
import { Clock, ChevronDown, ChevronUp } from "lucide-react";
import { calculateStorageData } from "@/lib/planUtils";

export default function UsageSummary({
  totalRemainingMinutes,
  isExpanded,
  summary,
  isAppSumoUser,
  showClickHint = false,
}) {
  const t = useTranslations("dashboard.remainingMinutes");

  // Get storage data from API summary (already in GB)
  const totalStorageGB = summary?.totalStorage || 0;
  const consumedStorageGB = summary?.consumedStorage || 0;
  // Fix floating point precision issues by using toFixed to limit decimal places
  const availableStorageGB = parseFloat(
    (totalStorageGB - consumedStorageGB).toFixed(2)
  );

  const storageData = {
    totalGB: totalStorageGB,
    usedGB: consumedStorageGB,
    availableGB: availableStorageGB,
  };

  return (
    <div className="w-full bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-gray-50/50 transition-colors cursor-pointer overflow-hidden">
      <div className="p-6">
        {/* Header with Title and Expand Button */}
        <div className="flex items-center justify-between mb-5">
          <h2 className="text-lg font-semibold">{t("usageSummary")}</h2>
          <div className="cursor-pointer text-gray-400 transition-transform duration-200">
            {isExpanded ? (
              <ChevronUp className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </div>
        </div>

        {/* 紧凑仪表盘布局 */}
        <div className={isAppSumoUser ? "space-y-5" : ""}>
          {/* Minutes Section */}
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="w-4 h-4 text-custom-bg mr-2" />
              <span className="text-base font-semibold text-custom-bg">
                {totalRemainingMinutes} {t("minutesRemaining")}
              </span>
            </div>
            <div className="flex items-center justify-between text-xs text-gray-600 mb-1.5">
              <span>
                {t("used")}: {summary?.consumedCredits || 0}
              </span>
              <span>
                {t("total")}: {summary?.totalCredits || 0}
              </span>
            </div>
            <div className="h-2.5 w-full bg-custom-bg/10 rounded-full overflow-hidden">
              <div
                className="h-full bg-custom-bg rounded-full transition-all duration-500 ease-in-out"
                style={{
                  width: `${Math.min(
                    ((summary?.consumedCredits || 0) / (summary?.totalCredits || 1)) * 100,
                    100
                  )}%`,
                }}
              />
            </div>
          </div>

          {/* Storage Section - Only show for AppSumo users */}
          {isAppSumoUser && (
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <svg
                  className="w-4 h-4 text-green-600 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7M4 7c0-2.21 1.79-4 4-4h8c2.21 0 4 1.79 4 4M4 7h16"
                  />
                </svg>
                <span className="text-base font-semibold text-green-900">
                  {storageData.availableGB} {t("gbAvailable")}
                </span>
              </div>
              <div className="flex items-center justify-between text-xs text-gray-600 mb-1.5">
                <span>
                  {t("storageUsed")}: {storageData.usedGB} GB
                </span>
                <span>
                  {t("storageTotal")}: {storageData.totalGB} GB
                </span>
              </div>
              <div className="h-2.5 w-full bg-green-100 rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 rounded-full transition-all duration-500 ease-in-out"
                  style={{
                    width: `${Math.min(
                      (storageData.usedGB / storageData.totalGB) * 100,
                      100
                    )}%`,
                  }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Click for details hint - Only show when requested */}
        {showClickHint && (
          <div className="mt-4 pt-3 border-t border-gray-100">
            <div className="text-xs text-gray-400 text-center">
              {t("clickForDetails")}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

