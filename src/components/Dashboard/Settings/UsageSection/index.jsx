"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";

import UsageSummary from "./UsageSummary";
import { SettingsPlanCard } from "./PlanCard";
import {
  PLAN_TYPES,
  transformEntitlementsToPlan,
  sortPlansByPriority,
  calculateTotalRemainingMinutes,
} from "@/lib/planUtils";

export default function UsageSection({ user }) {
  const { entitlements, summary, loading, error, fetchEntitlements } =
    useEntitlementsStore();
  const { openDialog } = useUpgradeDialogStore();
  const [isAccordionOpen, setIsAccordionOpen] = useState(false);
  const t = useTranslations("settings");
  const tDashboard = useTranslations("dashboard.remainingMinutes");

  // Check if user is AppSumo user
  const isAppSumoUser = user?.primaryPlanDetail?.isAppsumo || false;

  // Fetch entitlements data when component mounts
  useEffect(() => {
    fetchEntitlements();
  }, [fetchEntitlements]);

  // Transform entitlements data to plan objects
  const userPlans = entitlements?.map(transformEntitlementsToPlan) || [];

  // Sort plans by priority (lower number = higher priority)
  const sortedPlans = sortPlansByPriority(userPlans);

  // Calculate total remaining minutes
  const totalRemainingMinutes = calculateTotalRemainingMinutes(entitlements);

  // Handle manage plan action
  const handleManagePlan = (planType) => {
    switch (planType) {
      case PLAN_TYPES.FREE:
        openDialog("settings_usage");
        break;
      case PLAN_TYPES.SUBSCRIPTION:
        // Open subscription management
        if (typeof window !== "undefined") {
          window.open("/dashboard/billing", "_blank");
        }
        break;
      case PLAN_TYPES.ONE_TIME:
        openDialog("settings_usage");
        break;
      case PLAN_TYPES.LTD:
        // For LTD plans, show manage dialog or redirect
        if (typeof window !== "undefined") {
          window.open("/dashboard/billing", "_blank");
        }
        break;
      default:
        openDialog("settings_usage");
    }
  };

  if (loading) {
    return (
      <Card className="overflow-hidden" id="usage">
        <CardHeader className="bg-muted/50">
          <CardTitle className="text-xl">{t("usage.title")}</CardTitle>
          <CardDescription>{t("usage.description")}</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-8">
            <div className="text-sm text-muted-foreground">
              {tDashboard("loading")}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="overflow-hidden" id="usage">
        <CardHeader className="bg-muted/50">
          <CardTitle className="text-xl">{t("usage.title")}</CardTitle>
          <CardDescription>{t("usage.description")}</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center py-8 space-y-4">
            <div className="text-sm text-red-600">
              {tDashboard("errorLoading")}
            </div>
            <Button variant="outline" size="sm" onClick={() => fetchEntitlements()}>
              {tDashboard("tryAgain")}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden" id="usage">
      <CardHeader className="bg-muted/50">
        <CardTitle className="text-xl">{t("usage.title")}</CardTitle>
        <CardDescription>{t("usage.description")}</CardDescription>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Usage Summary */}
          <div className="cursor-pointer" onClick={() => setIsAccordionOpen(!isAccordionOpen)}>
            <UsageSummary
              totalRemainingMinutes={totalRemainingMinutes}
              isExpanded={isAccordionOpen}
              summary={summary}
              isAppSumoUser={isAppSumoUser}
              showClickHint={true}
            />
          </div>

          {/* Detailed Plan Cards - Only show when expanded */}
          {isAccordionOpen && sortedPlans && sortedPlans.length > 0 && (
            <div className="space-y-4 pt-4 border-t">
              <h4 className="text-sm font-medium text-gray-900">{tDashboard("details")}</h4>
              <div className="space-y-3">
                {sortedPlans.map((plan) => (
                  <SettingsPlanCard key={plan.id} plan={plan} onManagePlan={handleManagePlan} />
                ))}
              </div>
            </div>
          )}

          {/* Upgrade Button */}
          {totalRemainingMinutes < 60 && (
            <div className="pt-4 border-t">
              <Button onClick={() => openDialog("settings_usage")} className="w-full bg-custom-bg hover:bg-custom-bg-600">
                {tDashboard("upgradeNow")}
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

