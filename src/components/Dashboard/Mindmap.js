"use client";

import React, { useEffect, useRef, useState, useCallback } from "react";
import { createPortal } from "react-dom";
import { Markmap } from "markmap-view";
import { Transformer } from "markmap-lib";
import {
  Focus,
  ZoomOut,
  ZoomIn,
  Maximize2,
  Download,
  X,
  ChevronDown,
  Crown,
} from "lucide-react";
import { toPng } from "html-to-image";
import { useTranslations } from "next-intl";
import FeatureCheck from "@/components/Dashboard/FeatureCheck";
import { trackEvent } from "@/lib/analytics";
import { useAuthStore } from "@/stores/useAuthStore";
import { transcriptionService } from "@/services/api/transcriptionService";
import { Toast } from "@/components/ui/toast";
import { handleFileDownload } from "@/lib/downloadHelper";
import PremiumFeatureDialog from "@/components/Common/PremiumFeatureDialog";
import GuestModeDialog from "@/components/Dashboard/GuestModeDialog";

const MindMapContent = ({
  outline,
  filename,
  isAnonymous,
  transcriptionId,
}) => {
  const t = useTranslations("share.mind_map");
  const t2 = useTranslations("transcription");
  const { user } = useAuthStore();
  const containerRef = useRef(null);
  const svgRef = useRef(null);
  const markmapRef = useRef(null);
  const [initialRoot, setInitialRoot] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [toast, setToast] = useState(null);
  const [showPremiumDialog, setShowPremiumDialog] = useState(false);
  const [pendingShowPremiumDialog, setPendingShowPremiumDialog] =
    useState(false);
  const [showGuestModeDialog, setShowGuestModeDialog] = useState(false);
  const menuRef = useRef(null);

  const resetView = useCallback(
    (source = null) => {
      if (markmapRef.current && initialRoot) {
        try {
          markmapRef.current.setData(initialRoot);
          markmapRef.current.fit();

          // 埋点：只有当source为"focus_button"时才记录Focus按钮点击
          if (source === "focus_button") {
            trackEvent("mindmap_reset_view", {
              source: "mindmap_toolbar",
            });
          }
        } catch (error) {
          console.warn("Failed to reset markmap view:", error);
        }
      }
    },
    [markmapRef, initialRoot]
  );

  const zoomOut = () => {
    if (markmapRef.current) {
      try {
        markmapRef.current.rescale(1 / 1.2);

        // 埋点：缩小
        trackEvent("mindmap_zoom_out", {
          source: "mindmap_toolbar",
        });
      } catch (error) {
        console.warn("Failed to zoom out:", error);
      }
    }
  };

  const toggleFullscreen = useCallback(() => {
    const newFullscreenState = !isFullscreen;
    setIsFullscreen(newFullscreenState);

    // 埋点：只记录进入全屏
    if (newFullscreenState) {
      trackEvent("mindmap_enter_fullscreen", {
        source: "mindmap_toolbar",
      });
    }

    // 延迟重置视图，确保 DOM 更新完成
    setTimeout(resetView, 200);
  }, [resetView, isFullscreen]);

  const zoomIn = () => {
    if (markmapRef.current) {
      try {
        markmapRef.current.rescale(1.2);

        // 埋点：放大
        trackEvent("mindmap_zoom_in", {
          source: "mindmap_toolbar",
        });
      } catch (error) {
        console.warn("Failed to zoom in:", error);
      }
    }
  };

  const exportAsPng = useCallback(() => {
    if (svgRef.current) {
      // Ensure the graph is fitted so we export the full map
      resetView();

      // Use a higher pixel ratio so the exported image is crisp even when not fullscreen
      // Keep it within a safe upper bound to avoid memory issues on very large maps
      const pixelRatio = Math.min(
        4,
        Math.max(2, Math.ceil(window.devicePixelRatio || 1) * 2)
      );

      setTimeout(() => {
        toPng(svgRef.current, {
          backgroundColor: "white",
          pixelRatio, // upscale rendering for sharper image
        })
          .then((dataUrl) => {
            const link = document.createElement("a");
            link.download = `${filename}_${t("mind_map")}.png`;
            link.href = dataUrl;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 埋点：导出成功
            trackEvent("mindmap_export_success", {
              source: "mindmap_toolbar",
              format: "png",
              pixelRatio,
            });
          })
          .catch((err) => {
            console.error("Failed to export image:", err);

            // 埋点：导出失败
            trackEvent("mindmap_export_failed", {
              source: "mindmap_toolbar",
              format: "png",
              error: err.message,
            });

            // 给用户一个更友好的提示
            setToast({
              message:
                t2("export_image_failed") ||
                "Export image failed. Please try fullscreen export.",
              variant: "error",
              duration: 4000,
            });
          });
      }, 500);
    }
  }, [svgRef, filename, resetView, t2]);

  // 处理Toast自动消失
  useEffect(() => {
    if (toast) {
      const timer = setTimeout(() => {
        setToast(null);
      }, toast.duration || 3000);

      return () => clearTimeout(timer);
    }
  }, [toast]);

  // 导出outline功能
  const exportOutline = useCallback(
    async (format) => {
      if (!transcriptionId) return;

      try {
        const response = await transcriptionService.exportOutline(
          transcriptionId,
          format
        );

        // 使用通用的文件下载处理函数
        handleFileDownload(
          response.data,
          response.headers,
          `${filename}_mindmap.${format}`
        );

        // 埋点：导出成功
        trackEvent("mindmap_export_success", {
          source: "mindmap_toolbar",
          format: format,
        });
      } catch (error) {
        console.error("Failed to export outline:", error);

        // 显示错误toast
        setToast({
          message: `${t("export.export_failed")}`,
          variant: "error",
          duration: 5000,
        });

        // 埋点：导出失败
        trackEvent("mindmap_export_failed", {
          source: "mindmap_toolbar",
          format: format,
          error: error.data?.message || error.message,
        });
      }
    },
    [transcriptionId, filename, t]
  );

  // 处理导出菜单项点击
  const handleExportClick = useCallback(
    (format) => {
      // 匿名用户点击PNG导出时显示访客弹框
      if (isAnonymous && format === "png") {
        setShowExportMenu(false);
        setShowGuestModeDialog(true);
        return;
      }

      // 检查付费功能（包括匿名用户点击MD/XMIND时也显示升级弹框）
      if (!user?.hasPaidPlan && (format === "md" || format === "xmind")) {
        setShowExportMenu(false);
        if (isFullscreen) {
          setPendingShowPremiumDialog(true);
          setIsFullscreen(false);
        } else {
          setShowPremiumDialog(true);
        }
        return;
      }

      setShowExportMenu(false);
      if (format === "png") {
        exportAsPng();
      } else if (format === "md") {
        exportOutline("md");
      } else if (format === "xmind") {
        exportOutline("xmind");
      }
    },
    [user, isFullscreen, exportAsPng, exportOutline, isAnonymous]
  );

  // 处理访客弹框关闭
  const handleGuestModeDialogClose = () => {
    setShowGuestModeDialog(false);
  };

  // 监听isFullscreen变化，退出全屏后弹窗
  useEffect(() => {
    if (!isFullscreen && pendingShowPremiumDialog) {
      setShowPremiumDialog(true);
      setPendingShowPremiumDialog(false);
    }
  }, [isFullscreen, pendingShowPremiumDialog]);

  // 处理点击外部区域关闭菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        showExportMenu &&
        menuRef.current &&
        !menuRef.current.contains(event.target)
      ) {
        setShowExportMenu(false);
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [showExportMenu]);

  useEffect(() => {
    const initializeMarkmap = () => {
      if (!svgRef.current || !outline) return;

      if (markmapRef.current) {
        svgRef.current.innerHTML = "";
      }

      const transformer = new Transformer();
      const { root } = transformer.transform(outline);

      setInitialRoot(root);

      const markmapOptions = {
        autoFit: true,
        fitRatio: 0.9,
        center: true,
        color: () => "#6366f1",
      };

      markmapRef.current = Markmap.create(svgRef.current, markmapOptions, root);
    };

    // 延迟初始化，确保 DOM 已经渲染
    const timer = setTimeout(initializeMarkmap, 100);
    return () => clearTimeout(timer);
  }, [outline, isFullscreen]); // 添加 isFullscreen 依赖

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape" && isFullscreen) {
        toggleFullscreen();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isFullscreen, toggleFullscreen]);

  // 渲染 MindMap 内容
  const renderMindMapContent = () => (
    <div
      ref={containerRef}
      className={`w-full flex flex-col transition-all duration-300 ease-in-out animate-in fade-in-0 ${
        isFullscreen
          ? "fixed inset-0 z-[9999] bg-white zoom-in-95"
          : "h-[calc(100vh-220px)] relative overflow-hidden bg-white"
      }`}
    >
      {/* 全屏时显示左上角关闭按钮 */}
      {isFullscreen && (
        <button
          className="absolute top-6 left-6 p-2 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors duration-200 z-10"
          onClick={toggleFullscreen}
          title={t("exit_fullscreen")}
        >
          <X className="w-5 h-5 text-gray-600" />
        </button>
      )}

      <div className="absolute top-6 right-2 flex justify-end space-x-1">
        {/* 导出下拉菜单 */}
        <div ref={menuRef} className="relative export-menu-container">
          <button
            className="px-3 py-1.5 text-xs bg-custom-bg text-white rounded hover:bg-custom-bg-500 flex items-center"
            onClick={() => setShowExportMenu(!showExportMenu)}
            title={t("export.download")}
          >
            <Download className="w-4 h-4 mr-1" />
            <ChevronDown
              className={`w-3 h-3 transition-transform ${
                showExportMenu ? "rotate-180" : ""
              }`}
            />
          </button>

          {/* 下拉菜单 */}
          {showExportMenu && (
            <div className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-[200px] whitespace-nowrap">
              {/* PNG选项 */}
              <button
                className="w-full px-4 py-3 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center transition-colors"
                onClick={() => handleExportClick("png")}
              >
                <Download className="w-4 h-4 mr-3 flex-shrink-0" />
                <span>{t("export.image_file")}</span>
              </button>

              {/* MD选项 */}
              <button
                className="w-full px-4 py-3 text-left text-sm flex items-center transition-colors text-gray-700 hover:bg-gray-100 cursor-pointer"
                onClick={() => handleExportClick("md")}
              >
                <Download className="w-4 h-4 mr-3 flex-shrink-0" />
                <span>{t("export.markdown_file")}</span>
                {!user?.hasPaidPlan && (
                  <Crown className="h-4 w-4 ml-2 text-yellow-500" />
                )}
              </button>

              {/* XMIND选项 */}
              <button
                className="w-full px-4 py-3 text-left text-sm flex items-center transition-colors text-gray-700 hover:bg-gray-100 cursor-pointer"
                onClick={() => handleExportClick("xmind")}
              >
                <Download className="w-4 h-4 mr-3 flex-shrink-0" />
                <span>{t("export.xmind_file")}</span>
                {!user?.hasPaidPlan && (
                  <Crown className="h-4 w-4 ml-2 text-yellow-500" />
                )}
              </button>
            </div>
          )}
        </div>
        <button
          className="px-3 py-1.5 text-xs bg-custom-bg text-white rounded hover:bg-custom-bg-500 flex items-center"
          onClick={zoomIn}
          title={t("zoom_in")}
        >
          <ZoomIn className="w-4 h-4" />
        </button>
        <button
          className="px-3 py-1.5 text-xs bg-custom-bg text-white rounded hover:bg-custom-bg-500 flex items-center"
          onClick={zoomOut}
          title={t("zoom_out")}
        >
          <ZoomOut className="w-4 h-4" />
        </button>
        <button
          className="px-3 py-1.5 text-xs bg-custom-bg text-white rounded hover:bg-custom-bg-500 flex items-center"
          onClick={() => resetView("focus_button")}
          title={t("reset_size")}
        >
          <Focus className="w-4 h-4" />
        </button>
        <button
          className="px-3 py-1.5 text-xs bg-custom-bg text-white rounded hover:bg-custom-bg-500 flex items-center"
          onClick={toggleFullscreen}
          title={isFullscreen ? t("exit_fullscreen") : t("fullscreen_view")}
        >
          <Maximize2 className="w-4 h-4" />
        </button>
      </div>
      <div className="flex-grow flex justify-center items-center">
        <svg ref={svgRef} className="w-full h-full" />
      </div>

      {/* Toast 显示 */}
      {toast && (
        <Toast
          message={toast.message}
          variant={toast.variant}
          duration={toast.duration}
          onClose={() => setToast(null)}
        />
      )}

      {/* PremiumFeatureDialog 弹窗 */}
      <PremiumFeatureDialog
        isOpen={showPremiumDialog}
        onClose={() => setShowPremiumDialog(false)}
        featureName="Mindmap Export"
        source="mindmap_export"
      />

      {/* 访客模式弹框 */}
      <GuestModeDialog
        isOpen={showGuestModeDialog}
        onClose={handleGuestModeDialogClose}
        source="mindmap_export"
      />
    </div>
  );

  // 如果是全屏状态，使用 Portal 渲染到 body
  if (isFullscreen && typeof window !== "undefined") {
    return createPortal(renderMindMapContent(), document.body);
  }

  // 否则正常渲染
  return renderMindMapContent();
};

const MindMap = ({
  outline,
  filename,
  planConfig,
  isAnonymous,
  transcriptionId,
}) => {
  return (
    <FeatureCheck featureName="mind_map" planConfig={planConfig}>
      <MindMapContent
        outline={outline}
        filename={filename}
        isAnonymous={isAnonymous}
        transcriptionId={transcriptionId}
      />
    </FeatureCheck>
  );
};

export default MindMap;
