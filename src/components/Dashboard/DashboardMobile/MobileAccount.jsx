"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/navigation";
import { useAuthStore } from "@/stores/useAuthStore";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { subscriptionService } from "@/services/api/subscriptionService";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import { ChevronRight, Settings, Clock, HardDrive } from "lucide-react";
import { safeOpenUrl } from "@/lib/browserUtils";
import { Link } from "@/components/Common/Link";
import { trackEvent } from "@/lib/analytics";
import {
  calculateTotalRemainingMinutes,
  calculateStorageData,
} from "@/lib/planUtils";

export function MobileAccount() {
  const t = useTranslations("dashboard.mobileDashboard");
  const tSidebar = useTranslations("dashboard.sidebar");
  const { user } = useAuthStore();
  const { entitlements, summary, fetchEntitlements } = useEntitlementsStore();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { openDialog } = useUpgradeDialogStore();

  // 获取使用情况数据
  useEffect(() => {
    if (user) {
      fetchEntitlements();
    }
  }, [user, fetchEntitlements]);

  if (!user) return null;

  const displayName = user?.displayName || user?.fullName || "User";
  const initials = displayName
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase();

  // 获取计划显示信息
  const getPlanInfo = () => {
    if (!user) return { planName: "Free", showUpgrade: true };

    const planName = user?.primaryPlan || "Free";

    // 如果是 Free 或 Basic 计划，显示 Upgrade Plan 按钮
    // 如果是 Pro 或 AppSumo 用户，显示 Buy More Minutes 按钮
    const isProOrAppSumo =
      user?.primaryPlan?.toLowerCase().includes("pro") ||
      user?.primaryPlanDetail?.isAppsumo;
    const showUpgrade = !isProOrAppSumo;

    return { planName, showUpgrade };
  };

  // 处理点击 Current Plan 卡片跳转到 settings 页面的 usage 区域
  const handlePlanCardClick = () => {
    trackEvent("mobile_account_plan_card_click", {
      source: "mobile_account",
    });
    router.push("/settings#usage");
  };

  const handleSignOut = async () => {
    try {
      await useAuthStore.getState().signOut();
      router.push("/auth/signin");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const handleManageSubscription = async () => {
    try {
      setIsLoading(true);
      const { data } = await subscriptionService.createCustomerPortal();
      safeOpenUrl(data.url);
    } catch (error) {
      console.error("Error creating portal session:", error);
      setIsLoading(false);
    }
  };

  const handleBuyMoreMinutes = () => {
    openDialog({
      source: "mobile_account",
      defaultPlanType: "onetime",
    });
  };

  // 处理计划按钮点击
  const handlePlanButtonClick = () => {
    const { showUpgrade } = getPlanInfo();

    if (showUpgrade) {
      // 显示升级对话框，默认到 yearly 标签页
      trackEvent("mobile_account_plan_click", {
        action: "upgrade",
      });
      openDialog({
        source: "mobile_account",
        defaultPlanType: "yearly",
      });
    } else {
      // 显示购买更多分钟对话框，默认到 onetime 标签页
      trackEvent("mobile_account_plan_click", {
        action: "buy_more",
      });
      openDialog({
        source: "mobile_account",
        defaultPlanType: "onetime",
      });
    }
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* 用户信息区域 */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col gap-4">
          {/* 用户基本信息 */}
          <div className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={user?.avatarUrl} />
              <AvatarFallback>{initials}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium">{displayName}</span>
              <span className="text-sm text-gray-500">{user.email}</span>
            </div>
          </div>

          {/* Current Plan Section - 复用桌面端逻辑 */}
          <div
            className="rounded-xl p-4 border border-[#6366f1]/20 cursor-pointer hover:bg-[#6366f1]/5 transition-colors"
            onClick={handlePlanCardClick}
          >
            {/* Header with Plan name and arrow */}
            <div className="flex justify-between items-center mb-4">
              <span className="text-sm font-semibold text-gray-700">
                {tSidebar("plan.currentPlan")}
              </span>
              <div className="flex items-center gap-2">
                <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-semibold rounded-full">
                  {getPlanInfo().planName}
                </span>
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </div>
            </div>

            {/* Usage Information */}
            <div className="space-y-3">
              {/* Minutes Section */}
              <div>
                <div className="flex justify-between text-xs text-gray-500 mb-2">
                  <span>
                    {tSidebar("plan.used")}: {summary?.consumedCredits || 0}
                  </span>
                  <span>
                    {tSidebar("plan.total")}: {summary?.totalCredits || 0}
                  </span>
                </div>
                <div className="h-2 bg-indigo-100 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-indigo-600 rounded-full transition-all duration-300"
                    style={{
                      width: `${
                        summary?.totalCredits
                          ? ((summary.consumedCredits || 0) /
                              summary.totalCredits) *
                            100
                          : 0
                      }%`,
                    }}
                  />
                </div>
              </div>

              {/* Storage Section - Only for AppSumo users */}
              {user?.primaryPlanDetail?.isAppsumo && (
                <div>
                  <div className="flex justify-between text-xs text-gray-500 mb-2">
                    <span>
                      {tSidebar("plan.storageUsed")}:{" "}
                      {calculateStorageData(summary).usedGB} GB
                    </span>
                    <span>
                      {tSidebar("plan.storageTotal")}:{" "}
                      {calculateStorageData(summary).totalGB} GB
                    </span>
                  </div>
                  <div className="h-2 bg-green-100 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-600 rounded-full transition-all duration-300"
                      style={{
                        width: `${
                          calculateStorageData(summary).totalGB
                            ? (calculateStorageData(summary).usedGB /
                                calculateStorageData(summary).totalGB) *
                              100
                            : 0
                        }%`,
                      }}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Upgrade Plan Button */}
            <Button
              className="w-full bg-[#6366f1] hover:bg-[#5855eb] text-white font-medium rounded-full shadow-sm text-sm h-10 mt-4"
              onClick={(e) => {
                e.stopPropagation(); // 防止触发卡片点击事件
                handlePlanButtonClick();
              }}
            >
              {getPlanInfo().showUpgrade
                ? tSidebar("plan.upgradePlan")
                : tSidebar("plan.buyMoreMinutes")}
            </Button>
          </div>
        </div>
      </div>

      {/* 按钮列表区域 */}
      <div className="flex-1 p-6 space-y-4">
        {/* 其他按钮 */}
        {/* Settings 按钮 */}
        <Link href="/settings">
          <Button
            variant="ghost"
            className="w-full justify-between h-14 text-base font-medium hover:bg-gray-100"
          >
            <div className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              <span className="text-left">{t("settings")}</span>
            </div>
            <ChevronRight className="h-5 w-5" />
          </Button>
        </Link>

        {/* 始终显示 Buy More Minutes 按钮 */}
        <Button
          variant="ghost"
          className="w-full justify-between h-14 text-base font-medium hover:bg-gray-100"
          onClick={handleBuyMoreMinutes}
        >
          <span className="text-left">{t("buyMoreMinutes")}</span>
          <ChevronRight className="h-5 w-5" />
        </Button>

        {/* 如果用户有活跃订阅，显示 Manage Subscription 按钮 */}
        {user?.hasActiveSubscription && (
          <Button
            variant="ghost"
            className="w-full justify-between h-14 text-base font-medium hover:bg-gray-100"
            onClick={handleManageSubscription}
            disabled={isLoading}
          >
            <span className="text-left">{t("manageSubscription")}</span>
            <ChevronRight className="h-5 w-5" />
          </Button>
        )}

        <Button
          variant="ghost"
          className="w-full justify-between h-14 text-base font-medium hover:bg-gray-100"
          onClick={handleSignOut}
        >
          <span className="text-left">{t("signOut")}</span>
          <ChevronRight className="h-5 w-5" />
        </Button>
      </div>
    </div>
  );
}
