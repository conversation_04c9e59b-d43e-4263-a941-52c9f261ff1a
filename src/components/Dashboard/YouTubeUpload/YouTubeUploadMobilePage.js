"use client";

import { useRef } from "react";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import YouTubeUploadContent from "./YouTubeUploadContent";

/**
 * 移动端专用的 YouTube 上传页面
 * 全屏显示，不使用对话框
 */
const YouTubeUploadMobilePage = ({
  isOpen,
  onClose,
  onTranscribeSubmit,
  selectedFolderId = null,
}) => {
  const t = useTranslations("dashboard");
  const contentRef = useRef();

  if (!isOpen) return null;

  const handleClose = () => {
    // 重置内容状态
    if (contentRef.current) {
      contentRef.current.reset();
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-white z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="w-6 h-6" />
        </Button>
        <h1 className="text-lg font-semibold text-gray-900">
          {t("youtube_upload.title")}
        </h1>
        <div className="w-16" /> {/* Spacer for centering */}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 max-w-2xl mx-auto">
          <div className="mb-4">
            <p className="text-gray-600 text-sm text-center">
              {t("youtube_upload.description")}
            </p>
          </div>

          <YouTubeUploadContent
            ref={contentRef}
            onTranscribeSubmit={onTranscribeSubmit}
            selectedFolderId={selectedFolderId}
            showLimitDisplay={true}
          />
        </div>
      </div>
    </div>
  );
};

export default YouTubeUploadMobilePage;
