import { useState, useImperativeHandle, forwardRef, useRef } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { useTranslations } from "next-intl";
import YouTubeUploadContent from "./YouTubeUploadContent";

const YouTubeUploadDialog = forwardRef(
  ({ onTranscribeSubmit, selectedFolderId = null }, ref) => {
    const t = useTranslations("dashboard");
    const [isOpen, setIsOpen] = useState(false);
    const contentRef = useRef();

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      openDialog: () => setIsOpen(true),
      closeDialog: () => handleClose(),
    }));

    const handleClose = () => {
      // 重置内容状态
      if (contentRef.current) {
        contentRef.current.reset();
      }
      setIsOpen(false);
    };

    const handleOpenChange = (open) => {
      if (!open) {
        handleClose();
      } else {
        setIsOpen(open);
      }
    };

    return (
      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-[640px]">
          <DialogHeader>
            <DialogTitle>{t("youtube_upload.title")}</DialogTitle>
            <DialogDescription>
              {t("youtube_upload.description")}
            </DialogDescription>
          </DialogHeader>

          <YouTubeUploadContent
            ref={contentRef}
            onTranscribeSubmit={(transcription) => {
              onTranscribeSubmit(transcription);
              handleClose();
            }}
            selectedFolderId={selectedFolderId}
            showLimitDisplay={true}
          />
        </DialogContent>
      </Dialog>
    );
  }
);

YouTubeUploadDialog.displayName = "YouTubeUploadDialog";

export default YouTubeUploadDialog;
