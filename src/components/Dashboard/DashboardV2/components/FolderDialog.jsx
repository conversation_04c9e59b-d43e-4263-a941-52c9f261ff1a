import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON>itle,
  <PERSON>alogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTranslations } from "next-intl";

export default function FolderDialog({
  isOpen,
  onClose,
  onSubmit,
  folder = null,
  mode = "create", // "create" or "edit"
}) {
  const t = useTranslations("folder.dialog");
  const [folderName, setFolderName] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const isEditMode = mode === "edit";
  const title = isEditMode ? t("edit.title") : t("create.title");
  const submitText = isEditMode ? t("edit.submit") : t("create.submit");
  const loadingText = isEditMode ? t("edit.loading") : t("create.loading");

  useEffect(() => {
    if (isEditMode && folder) {
      setFolderName(folder.name || "");
    } else {
      setFolderName("");
    }
  }, [isEditMode, folder, isOpen]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!folderName.trim()) return;

    // 检查字符长度限制（40个Unicode字符）
    if ([...folderName.trim()].length > 40) {
      alert(t("common.characterLimitError"));
      return;
    }

    setIsLoading(true);
    try {
      let success = false;
      if (isEditMode) {
        success = await onSubmit?.(folder.id, folderName.trim());
      } else {
        success = await onSubmit?.(folderName.trim());
      }

      // 只有操作成功时才关闭弹框
      if (success !== false) {
        handleClose();
      }
    } catch (error) {
      console.error(
        `Error ${isEditMode ? "editing" : "creating"} folder:`,
        error
      );
      // 操作失败时不关闭弹框，让用户可以重试
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    // 重置表单状态
    if (isEditMode && folder) {
      setFolderName(folder.name || "");
    } else {
      setFolderName("");
    }
    setIsLoading(false); // 确保重置loading状态
    onClose();
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          handleClose();
        }
      }}
    >
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="folderName">{t("common.folderName")}</Label>
              <Input
                id="folderName"
                value={folderName}
                onChange={(e) => setFolderName(e.target.value)}
                placeholder={t("common.placeholder")}
                maxLength={40}
                autoFocus
              />
              <p className="text-xs text-gray-500">
                {t("common.characterLimit", {
                  current: [...folderName].length,
                })}
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              {t("common.cancel")}
            </Button>
            <Button
              type="submit"
              disabled={!folderName.trim() || isLoading}
              className="bg-custom-bg hover:bg-custom-bg/90"
            >
              {isLoading ? loadingText : submitText}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
