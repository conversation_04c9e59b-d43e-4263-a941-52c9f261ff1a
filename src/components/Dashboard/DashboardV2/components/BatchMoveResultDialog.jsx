import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";

export default function BatchMoveResultDialog({ isOpen, onClose, moveResult }) {
  const t = useTranslations("folder.dialog.batchMoveResult");

  if (!moveResult) return null;

  const { total, moved, failed, errors = [] } = moveResult;
  const hasErrors = failed > 0;
  const isPartialSuccess = moved > 0 && failed > 0;
  const isCompleteSuccess = moved > 0 && failed === 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg font-semibold">
            {isCompleteSuccess && (
              <>
                <CheckCircle className="w-5 h-5 text-green-500" />
                {t("successTitle")}
              </>
            )}
            {isPartialSuccess && (
              <>
                <AlertCircle className="w-5 h-5 text-yellow-500" />
                {t("partialSuccessTitle")}
              </>
            )}
            {!moved && hasErrors && (
              <>
                <XCircle className="w-5 h-5 text-red-500" />
                {t("failureTitle")}
              </>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="py-4">
          {/* 统计信息 */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {total}
                </div>
                <div className="text-xs text-gray-500">{t("total")}</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-green-600">
                  {moved}
                </div>
                <div className="text-xs text-gray-500">{t("moved")}</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-red-600">
                  {failed}
                </div>
                <div className="text-xs text-gray-500">{t("failed")}</div>
              </div>
            </div>
          </div>

          {/* 成功消息 */}
          {moved > 0 && (
            <div className="mb-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-700">
                  {t("successMessage", { count: moved })}
                </span>
              </div>
            </div>
          )}

          {/* 错误详情 */}
          {hasErrors && errors.length > 0 && (
            <div className="mb-3">
              <div className="mb-2 flex items-center gap-2">
                <XCircle className="w-4 h-4 text-red-500" />
                <span className="text-sm font-medium text-red-700">
                  {t("errorDetails")}
                </span>
              </div>
              <div className="max-h-32 overflow-y-auto space-y-1">
                {errors.map((error, index) => (
                  <div
                    key={index}
                    className="p-2 bg-red-50 border border-red-200 rounded text-xs"
                  >
                    <div className="font-medium text-red-700">
                      ID: {error.transcriptionId}
                    </div>
                    <div className="text-red-600">{error.error}</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            onClick={onClose}
            className="w-full bg-indigo-600 hover:bg-indigo-700"
          >
            {t("close")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
