"use client";

import { useState } from "react";
import {
  Loader2,
  ChevronDown,
  MessageSquare,
  Settings,
  LogOut,
  Receipt,
} from "lucide-react";
import Image from "next/image";
import { useAuthStore } from "@/stores/useAuthStore";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import FeedbackSurvey from "@/components/Survey/FeedbackSurvey";
import { useProfileEntry } from "@/hooks/useProfileEntry";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/navigation";
import { trackEvent } from "@/lib/analytics";
import { safeOpenUrl } from "@/lib/browserUtils";

const ProfileEntryV2 = () => {
  const t = useTranslations("dashboard.profileEntry");
  const tSidebar = useTranslations("dashboard.sidebar");
  const { user } = useAuthStore();
  const router = useRouter();

  // 初始化状态：检查是否是首次访问和当前会话状态
  const [isExpanded, setIsExpanded] = useState(() => {
    // 首先检查当前会话中是否有保存的状态
    const sessionState = sessionStorage.getItem("profileCardExpanded");
    if (sessionState !== null) {
      return sessionState === "true";
    }

    // 如果会话中没有状态，检查是否是首次访问
    const hasVisitedBefore = localStorage.getItem("profileCardVisited");
    if (!hasVisitedBefore) {
      // 首次访问，标记已访问并展开
      localStorage.setItem("profileCardVisited", "true");
      sessionStorage.setItem("profileCardExpanded", "true");
      return true;
    }

    // 已访问过的用户，默认折叠
    sessionStorage.setItem("profileCardExpanded", "false");
    return false;
  });

  // 当展开状态改变时，保存到sessionStorage
  const handleToggleExpanded = (newState) => {
    setIsExpanded(newState);
    sessionStorage.setItem("profileCardExpanded", newState.toString());
  };
  const {
    isSigningOut,
    isManagingSubscription,
    handleSignOut,
    handleManageSubscription,
  } = useProfileEntry();

  if (!user) return null;

  const displayName = user?.displayName || user?.fullName || "User";

  // Generate initials for avatar fallback
  const initials = displayName
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);

  // Handle feedback functionality from sidebar
  const handleFeedback = () => {
    trackEvent("sidebar_click", {
      item: "feedback",
    });
    const { openSurvey } = FeedbackSurvey(user, null, "sidebar");
    openSurvey();
  };

  // Handle Discord navigation
  const handleDiscord = () => {
    trackEvent("sidebar_click", {
      item: "discord",
    });
    safeOpenUrl("https://discord.gg/RJTaS28UWU");
  };

  // Handle settings navigation
  const handleSettings = () => {
    trackEvent("sidebar_click", {
      item: "settings",
    });
    router.push("/settings");
  };

  return (
    <div className="w-full">
      <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
        <div className="flex items-center gap-3">
          <Avatar className="w-10 h-10">
            {user?.avatarUrl ? (
              <AvatarImage src={user.avatarUrl} alt="Avatar" />
            ) : null}
            <AvatarFallback className="bg-[#6366f1] text-white font-semibold">
              {initials}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="font-semibold text-gray-900 text-sm truncate">
              {displayName}
            </div>
            <div className="text-xs text-gray-500 truncate">{user.email}</div>
          </div>
          {/* Expand/Collapse button */}
          <Button
            variant="ghost"
            size="sm"
            className="w-8 h-8 p-0 text-gray-500 hover:bg-gray-100 rounded-lg"
            onClick={() => handleToggleExpanded(!isExpanded)}
          >
            <ChevronDown
              className={`w-4 h-4 transition-transform ${
                isExpanded ? "rotate-180" : ""
              }`}
            />
          </Button>
        </div>

        {/* Collapsible buttons section */}
        {isExpanded && (
          <div className="mt-4 space-y-2 animate-in slide-in-from-top-2 duration-200">
            {/* Manage Subscription - Only for subscription users */}
            {user?.hasActiveSubscription && (
              <Button
                variant="ghost"
                className="w-full justify-start text-gray-600 hover:bg-gray-100 rounded-xl"
                onClick={handleManageSubscription}
                disabled={isManagingSubscription}
              >
                {isManagingSubscription ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {t("redirecting")}
                  </>
                ) : (
                  <>
                    <Receipt className="w-4 h-4 mr-2" />
                    {t("manageSubscription")}
                  </>
                )}
              </Button>
            )}

            <Button
              variant="ghost"
              className="w-full justify-start text-gray-600 hover:bg-gray-100 rounded-xl"
              onClick={handleFeedback}
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              {tSidebar("feedback")}
            </Button>

            {/* Discord - Available for all users */}
            <Button
              variant="ghost"
              className="w-full justify-start text-gray-600 hover:bg-gray-100 rounded-xl"
              onClick={handleDiscord}
            >
              <Image
                src="/badges/Discord-Symbol-Blurple.svg"
                alt="Discord"
                width={16}
                height={16}
                className="mr-2 brightness-0 opacity-60"
              />
              Discord
            </Button>
            <Button
              variant="ghost"
              className="w-full justify-start text-gray-600 hover:bg-gray-100 rounded-xl"
              onClick={handleSettings}
            >
              <Settings className="w-4 h-4 mr-2" />
              {tSidebar("settings")}
            </Button>
            <Button
              variant="ghost"
              className="w-full justify-start text-gray-600 hover:bg-gray-100 rounded-xl"
              onClick={handleSignOut}
              disabled={isSigningOut}
            >
              {isSigningOut ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t("signingOut")}
                </>
              ) : (
                <>
                  <LogOut className="w-4 h-4 mr-2" />
                  {t("signOut")}
                </>
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfileEntryV2;
