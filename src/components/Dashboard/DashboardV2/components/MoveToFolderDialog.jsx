import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FolderOpen, HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTranslations } from "next-intl";
import { folderService } from "@/services/api/folderService";
import { transcriptionService } from "@/services/api/transcriptionService";
import { toast } from "sonner";

export default function MoveToFolderDialog({
  isOpen,
  onClose,
  transcription,
  transcriptions, // 批量移动时的转录记录数组（已废弃，保留向后兼容）
  transcriptionIds, // 批量移动时的转录记录ID数组（新方式）
  currentFolderId, // 当前文件夹ID，用于判断文件夹选择逻辑
  onMoveSuccess,
}) {
  const t = useTranslations("folder.dialog.move");
  const [folders, setFolders] = useState([]);
  const [selectedFolderId, setSelectedFolderId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFolders, setIsLoadingFolders] = useState(false);
  const [targetTranscriptions, setTargetTranscriptions] = useState([]);
  // 判断是否为批量移动模式
  const isBatchMode =
    (transcriptionIds && transcriptionIds.length > 0) ||
    (transcriptions && transcriptions.length > 0);

  // 获取文件夹列表
  const fetchFolders = async () => {
    try {
      setIsLoadingFolders(true);
      const response = await folderService.getFolders();
      const { folders: apiFolders } = response.data;
      setFolders(apiFolders);
    } catch (error) {
      console.error("Error fetching folders:", error);
    } finally {
      setIsLoadingFolders(false);
    }
  };

  // 组件打开时获取文件夹列表和转录记录详情
  useEffect(() => {
    if (isOpen) {
      fetchFolders();
      setSelectedFolderId(null);

      // 设置目标转录记录
      if (transcriptionIds && transcriptionIds.length > 0) {
        // 使用新的ID数组方式
        const transcriptionObjects = transcriptionIds.map((id) => ({
          id: String(id),
        }));
        setTargetTranscriptions(transcriptionObjects);
      } else if (transcriptions && transcriptions.length > 0) {
        // 向后兼容：使用旧的对象数组方式
        setTargetTranscriptions(transcriptions);
      } else if (transcription) {
        // 单个转录记录
        setTargetTranscriptions([transcription]);
      } else {
        setTargetTranscriptions([]);
      }
    }
  }, [isOpen, transcriptionIds, transcriptions, transcription]);

  const handleSubmit = async () => {
    if (selectedFolderId === undefined) return;

    // 检查是否有有效的转录记录
    if (isBatchMode && targetTranscriptions.length === 0) return;
    if (!isBatchMode && !transcription) return;

    setIsLoading(true);
    try {
      if (isBatchMode) {
        // 批量移动
        const ids = targetTranscriptions.map((t) => String(t.id));
        await transcriptionService.batchMoveToFolder(ids, selectedFolderId);

        // 显示成功提示
        toast.success(t("batchMoveSuccess", { count: ids.length }));

        // 调用成功回调，用于刷新列表
        onMoveSuccess?.(null, selectedFolderId, isBatchMode);
      } else {
        // 单个移动
        await transcriptionService.moveToFolder(
          String(transcription.id),
          selectedFolderId
        );

        // 显示成功提示
        toast.success(t("moveSuccess"));

        // 调用成功回调，用于刷新列表
        onMoveSuccess?.(transcription.id, selectedFolderId, false);
      }
      onClose();
    } catch (error) {
      console.error("Error moving transcription:", error);

      // 显示错误提示
      if (isBatchMode) {
        toast.error(t("batchMoveError"));
      } else {
        toast.error(t("moveError"));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedFolderId(null);
    setIsLoading(false);
    onClose();
  };

  // 判断文件夹是否可选
  const isFolderSelectable = (folderId) => {
    if (isBatchMode) {
      // 批量移动模式
      if (targetTranscriptions.length === 0) return false;

      // 如果使用transcriptionIds方式，使用currentFolderId来判断
      if (transcriptionIds && transcriptionIds.length > 0) {
        // 如果当前在特定文件夹中，且目标文件夹是当前文件夹，则不可选
        if (currentFolderId !== "all" && currentFolderId === folderId) {
          return false;
        }
        // 如果当前在"All Files"中，且目标是未分类，需要特殊处理
        // 这种情况下我们无法确定所有文件是否都在未分类中，所以允许选择
        return true;
      }

      // 如果使用transcriptions方式（有folderId信息），检查文件夹选择逻辑
      const allInSameFolder = targetTranscriptions.every(
        (t) => t.folderId === targetTranscriptions[0].folderId
      );
      if (allInSameFolder && targetTranscriptions[0].folderId === folderId) {
        return false;
      }
      // 如果所有文件都在未分类状态，不能选择未分类
      if (
        allInSameFolder &&
        targetTranscriptions[0].folderId === null &&
        folderId === null
      ) {
        return false;
      }
      return true;
    } else {
      // 单个移动模式：保持原有逻辑
      if (!transcription) return false;
      if (transcription.folderId === null && folderId === null) {
        return false;
      }
      return folderId !== transcription.folderId;
    }
  };

  // 获取当前转录记录所在的文件夹名称
  const getCurrentFolderName = () => {
    if (isBatchMode) {
      // 批量移动模式：显示选中的文件数量
      return `${targetTranscriptions.length} files selected`;
    } else {
      if (!transcription) {
        return "No file selected";
      }
      if (transcription.folderId === null) {
        return t("uncategorized");
      }
      return transcription.folderName || "Unknown Folder";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-gray-900">
            {t("title")}
          </DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <div className="mb-4">
            <p className="text-sm text-gray-600">
              {isBatchMode
                ? t("movingSelectedFiles", {
                    count: targetTranscriptions.length,
                  })
                : transcription
                ? t("movingFromFolder", {
                    filename: transcription.filename,
                    folderName: getCurrentFolderName(),
                  })
                : t("noFileSelected")}
            </p>
          </div>

          {isLoadingFolders ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#6366f1]" />
            </div>
          ) : (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {/* 未分类选项 */}
              {isFolderSelectable(null) && (
                <div
                  className={`flex items-center p-3 rounded-lg cursor-pointer transition-all border-2 ${
                    selectedFolderId === null
                      ? "bg-indigo-50 border-indigo-500"
                      : "bg-gray-50 hover:bg-gray-100 border-transparent"
                  }`}
                  onClick={() => setSelectedFolderId(null)}
                >
                  <FolderOpen className="w-4 h-4 text-gray-500 mr-3" />
                  <span className="text-sm font-medium text-gray-700 flex-1">
                    {t("uncategorized")}
                  </span>
                </div>
              )}

              {/* 用户文件夹列表 */}
              {folders.map((folder) => {
                const isSelectable = isFolderSelectable(folder.id);
                const isSelected = selectedFolderId === folder.id;

                return (
                  <div
                    key={folder.id}
                    className={`flex items-center p-3 rounded-lg transition-all border-2 ${
                      !isSelectable
                        ? "bg-gray-100 cursor-not-allowed opacity-50 border-transparent"
                        : isSelected
                        ? "bg-indigo-50 border-indigo-500 cursor-pointer"
                        : "bg-gray-50 hover:bg-gray-100 border-transparent cursor-pointer"
                    }`}
                    onClick={() =>
                      isSelectable && setSelectedFolderId(folder.id)
                    }
                  >
                    <FolderOpen className="w-4 h-4 text-gray-500 mr-3" />
                    <span className="text-sm font-medium text-gray-700">
                      {folder.name}
                    </span>
                    {!isSelectable && (
                      <span className="text-xs text-gray-400 ml-2">
                        {t("currentFolder")}
                      </span>
                    )}
                  </div>
                );
              })}

              {folders.length === 0 && !isLoadingFolders && (
                <div className="text-center py-8 text-gray-500">
                  <p className="text-sm">{t("noFoldersAvailable")}</p>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
            className="flex-1"
          >
            {t("cancel")}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              isLoading || selectedFolderId === undefined || isLoadingFolders
            }
            className="flex-1 bg-indigo-600 hover:bg-indigo-700"
          >
            {isLoading ? t("loading") : t("submit")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
