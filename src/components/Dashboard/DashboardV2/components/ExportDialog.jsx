"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Download, Info, Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { transcriptionService } from "@/services/api/transcriptionService";
import { shareService } from "@/services/api/shareService";
import { trackEvent } from "@/lib/analytics";
import { handleFileDownload } from "@/lib/downloadHelper";
import { toast } from "sonner";

// 导出对话框内容组件 - 可被ExportFileMenu和ExportDialog复用
export function ExportDialogContent({
  fileId,
  fileIds = null, // 批量导出时的文件ID数组
  isBatchMode = false, // 是否为批量模式
  selectedCount = 0, // 批量模式下选中的文件数量
  transcriptionType = null,
  insufficientMinutes = 0,
  isSharedPage = false,
  exportSource = "unknown", // 导出操作入口：filelist_action, transcription_detail, share_page
  onExportComplete,
  onCancel, // 取消回调
}) {
  const t = useTranslations("dashboard.export");
  const tFileList = useTranslations("dashboard.fileList");

  // 状态管理
  const [transcriptEnabled, setTranscriptEnabled] = useState(true);
  const [transcriptFormat, setTranscriptFormat] = useState("txt");
  const [showSpeaker, setShowSpeaker] = useState(false);
  const [showTimestamp, setShowTimestamp] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  const hasIncompleteContent = insufficientMinutes > 0;

  // 转录格式定义
  const transcriptFormats = [
    { value: "txt", label: t("txt") },
    { value: "docx", label: t("docx") },
    { value: "pdf", label: t("pdf") },
    { value: "csv", label: t("csv") },
    {
      value: "srt",
      label: t("srt"),
      needsWarning: transcriptionType === "transcript",
    },
    {
      value: "vtt",
      label: t("vtt"),
      needsWarning: transcriptionType === "transcript",
    },
  ];

  // 获取当前选择的转录格式信息
  const selectedTranscriptFormat = transcriptFormats.find(
    (f) => f.value === transcriptFormat
  );

  // 判断是否显示导出选项（非字幕格式才显示）
  const shouldShowExportOptions = () => {
    return transcriptFormat !== "srt" && transcriptFormat !== "vtt";
  };

  const handleExport = async () => {
    if (!transcriptEnabled || isExporting) return;

    setIsExporting(true);
    try {
      if (isBatchMode) {
        await exportBatchTranscriptFiles(fileIds, transcriptFormat, {
          showSpeaker,
          showTimestamp,
        });
      } else {
        await exportTranscriptFile(fileId, transcriptFormat, {
          showSpeaker,
          showTimestamp,
        });
      }

      // 通知父组件导出完成
      if (onExportComplete) {
        onExportComplete();
      }
    } catch (error) {
      console.error("Export failed:", error);
      if (isBatchMode) {
        toast.error(tFileList("batchExportError"));
      }
    } finally {
      setIsExporting(false);
    }
  };

  const exportTranscriptFile = async (fileId, format, options = {}) => {
    try {
      let response;

      if (isSharedPage) {
        response = await shareService.exportShareTranscription(fileId, format, {
          showSpeaker: options.showSpeaker,
          showTimestamp: options.showTimestamp,
        });
      } else {
        response = await transcriptionService.exportTranscription(
          fileId,
          format,
          {
            showSpeaker: options.showSpeaker,
            showTimestamp: options.showTimestamp,
          }
        );
      }

      if (response.status === 200) {
        handleFileDownload(
          response.data,
          response.headers,
          `exported_file.${format}`
        );

        // 追踪成功导出事件
        trackEvent("export_success", {
          format,
          type: "transcript",
          source: exportSource,
          ...options,
        });
      } else {
        throw new Error("Export transcript failed");
      }
    } catch (error) {
      // 追踪导出失败事件
      trackEvent("export_error", {
        fileId,
        format,
        type: "transcript",
        source: exportSource,
        error: error?.message || String(error),
      });
      console.error(error);
    }
  };

  const exportBatchTranscriptFiles = async (fileIds, format, options = {}) => {
    try {
      const response = await transcriptionService.batchExportTranscriptions(
        fileIds,
        format,
        {
          showSpeaker: options.showSpeaker,
          showTimestamp: options.showTimestamp,
        }
      );

      if (response.status === 200) {
        // 处理文件下载
        handleFileDownload(
          response.data,
          response.headers,
          `batch_export.${format}`
        );

        // 从响应头获取导出结果信息
        const totalCount = parseInt(response.headers["x-export-total"] || "0");
        const successCount = parseInt(
          response.headers["x-export-successful"] || "0"
        );
        const failedCount = parseInt(
          response.headers["x-export-failed"] || "0"
        );
        const hasFailures =
          response.headers["x-export-has-failures"] === "true";

        // 显示结果提示
        if (hasFailures && failedCount > 0) {
          // 部分成功
          toast.success(
            tFileList("batchExportPartialSuccess", {
              successCount,
              failedCount,
            })
          );
        } else {
          // 全部成功
          toast.success(
            tFileList("batchExportSuccess", {
              successCount,
            })
          );
        }

        // 追踪成功导出事件
        trackEvent("batch_export_success", {
          format,
          totalCount,
          successCount,
          failedCount,
          ...options,
        });
      } else {
        throw new Error("Batch export failed");
      }
    } catch (error) {
      console.error("Batch export failed:", error);
      throw error;
    }
  };

  return (
    <>
      <div className="px-6 space-y-6 min-h-[300px] transition-all duration-300 ease-in-out">
        {/* 不完整内容警告 */}
        {hasIncompleteContent && (
          <div className="px-3 py-2 mb-1 bg-amber-50 text-amber-700 text-xs rounded flex items-center whitespace-normal max-w-[300px] overflow-wrap-anywhere">
            <Info className="h-3.5 w-3.5 mr-1.5 flex-shrink-0" />
            <span>
              {t("incomplete_export_warning", {
                minutes: insufficientMinutes,
              })}
            </span>
          </div>
        )}

        {/* Transcript Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-base font-medium">
                {t("transcript_section_title")}
              </Label>
              <p className="text-sm text-muted-foreground">
                {t("transcript_section_description")}
              </p>
            </div>
            <Switch
              checked={transcriptEnabled}
              onCheckedChange={setTranscriptEnabled}
              className="data-[state=checked]:bg-custom-bg-500"
            />
          </div>

          {transcriptEnabled && (
            <div className="space-y-4 pl-4 transition-all duration-300 ease-in-out">
              <div className="space-y-2">
                <Label
                  htmlFor="transcript-format"
                  className="text-sm font-medium"
                >
                  {t("file_format_label")}
                </Label>
                <Select
                  value={transcriptFormat}
                  onValueChange={setTranscriptFormat}
                >
                  <SelectTrigger className="border border-gray-200 hover:border-custom-bg">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {transcriptFormats.map((format) => (
                      <SelectItem key={format.value} value={format.value}>
                        <span>{format.label}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 导出选项 - 仅对非字幕格式显示 */}
              {shouldShowExportOptions() && (
                <div className="space-y-3">
                  <Label className="text-sm font-medium">
                    {t("export_options_label")}
                  </Label>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="show-speaker"
                        checked={showSpeaker}
                        onCheckedChange={setShowSpeaker}
                        className="data-[state=checked]:bg-custom-bg-500 data-[state=checked]:border-custom-bg-500"
                      />
                      <Label htmlFor="show-speaker" className="text-sm">
                        {t("show_speaker_names")}
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="show-timestamp"
                        checked={showTimestamp}
                        onCheckedChange={setShowTimestamp}
                        className="data-[state=checked]:bg-custom-bg-500 data-[state=checked]:border-custom-bg-500"
                      />
                      <Label htmlFor="show-timestamp" className="text-sm">
                        {t("show_timestamps")}
                      </Label>
                    </div>
                  </div>
                </div>
              )}

              {/* 字幕格式警告 */}
              {selectedTranscriptFormat?.needsWarning && (
                <div className="px-3 py-2 bg-amber-50 text-amber-700 text-xs rounded flex items-center">
                  <Info className="h-3.5 w-3.5 mr-1.5 flex-shrink-0" />
                  <span>{t("subtitle_warning")}</span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-end p-6 pt-4 border-t bg-gray-50/50">
        <div className="flex gap-3">
          <Button variant="outline" onClick={() => onCancel && onCancel()}>
            {t("cancel")}
          </Button>
          <Button
            className="bg-custom-bg hover:bg-custom-bg/90"
            disabled={!transcriptEnabled || isExporting}
            onClick={handleExport}
          >
            {isExporting ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Download className="w-4 h-4 mr-2" />
            )}
            {isExporting ? `${t("export")}...` : t("export")}
          </Button>
        </div>
      </div>
    </>
  );
}

// 简单的导出对话框组件
export default function ExportDialog({
  isOpen,
  onOpenChange,
  fileId,
  fileIds = null, // 批量导出时的文件ID数组
  isBatchMode = false, // 是否为批量模式
  selectedCount = 0, // 批量模式下选中的文件数量
  transcriptionType = null,
  insufficientMinutes = 0,
  exportSource = "filelist_action", // 默认为文件列表操作
  onExportComplete = null, // 导出完成回调
}) {
  const t = useTranslations("dashboard.export");

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] p-0">
        <DialogHeader className="p-6 pb-4">
          <DialogTitle className="text-xl font-semibold">
            {t("dialog_title")}
          </DialogTitle>
          <DialogDescription>
            {isBatchMode
              ? t("batch_export_description", { count: selectedCount })
              : t("dialog_description")}
          </DialogDescription>
        </DialogHeader>

        <ExportDialogContent
          fileId={fileId}
          fileIds={fileIds}
          isBatchMode={isBatchMode}
          selectedCount={selectedCount}
          transcriptionType={transcriptionType}
          insufficientMinutes={insufficientMinutes}
          exportSource={exportSource}
          onExportComplete={onExportComplete}
          onCancel={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
