import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";

export function SidebarSkeleton({ className }) {
  return (
    <div
      className={cn(
        "w-full md:w-[260px] border-r border-gray-200 bg-white flex flex-col py-4 h-full animate-pulse",
        className
      )}
    >
      {/* Header */}
      <div className="px-4 flex-shrink-0">
        {/* Logo skeleton */}
        <div className="mb-5">
          <Skeleton className="h-8 w-32" />
        </div>

        {/* Plan Section skeleton */}
        <div className="bg-muted/30 rounded-2xl p-3 border border-muted mb-6">
          <div className="flex justify-between items-center mb-3">
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-5 w-12 rounded-full" />
          </div>
          <Skeleton className="w-full h-9 rounded-xl" />
        </div>
      </div>

      {/* Folder List skeleton */}
      <div className="flex-1 min-h-0 px-4">
        <div className="space-y-3">
          {/* Navigation items skeleton */}
          <div className="space-y-2">
            <Skeleton className="h-4 w-20 mb-3" />
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton className="w-4 h-4" />
                <Skeleton className="h-4 w-24" />
              </div>
            ))}
          </div>

          {/* Folders skeleton */}
          <div className="space-y-2 mt-6">
            <Skeleton className="h-4 w-16 mb-3" />
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton className="w-4 h-4" />
                <Skeleton className="h-4 w-20" />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Footer - Profile skeleton */}
      <div className="px-4 flex-shrink-0">
        <div className="flex items-center space-x-3 p-2">
          <Skeleton className="w-8 h-8 rounded-full" />
          <div className="flex-1">
            <Skeleton className="h-4 w-20 mb-1" />
            <Skeleton className="h-3 w-16" />
          </div>
          <Skeleton className="w-4 h-4" />
        </div>
      </div>
    </div>
  );
}
