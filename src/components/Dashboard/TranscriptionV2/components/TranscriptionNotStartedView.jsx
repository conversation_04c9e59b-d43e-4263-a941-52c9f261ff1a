"use client";

import { useState, useCallback } from "react";
import { useTranslations } from "next-intl";
import { TranscriptionLayout } from "./Layout";
import { Header } from "./Header";
import { transcriptionService } from "@/services/api/transcriptionService";
import { trackEvent } from "@/lib/analytics";

/**
 * 转录未开始状态的视图组件
 * 显示准备转录的界面，提供手动开始转录的功能
 */
const TranscriptionNotStartedView = ({
  transcription,
  planConfig,
  onShare,
  onTranscriptionStarted,
}) => {
  const t = useTranslations("transcription");
  const [isStartingTranscription, setIsStartingTranscription] = useState(false);

  // 手动开始转录
  const handleStartTranscription = useCallback(async () => {
    if (!transcription?.id || isStartingTranscription) return;

    try {
      setIsStartingTranscription(true);
      await transcriptionService.doTranscription(transcription.id);

      // Track successful transcription start
      trackEvent("transcription_not_started_start_success", {
        sourceType: transcription.sourceType,
      });

      // 重新获取转录数据以更新状态
      const response = await transcriptionService.getTranscriptionById(
        transcription.id
      );
      if (response.data && onTranscriptionStarted) {
        onTranscriptionStarted(response.data);
      }
    } catch (error) {
      console.error("Failed to start transcription:", error);

      // Track transcription start failure
      trackEvent("transcription_not_started_start_failed", {
        error: error?.message || "Unknown error",
      });

      // TODO: 显示错误提示
    } finally {
      setIsStartingTranscription(false);
    }
  }, [transcription?.id, isStartingTranscription, onTranscriptionStarted]);

  return (
    <TranscriptionLayout
      header={
        <Header
          title={transcription.filename}
          fileId={transcription.id}
          isYouTube={transcription.sourceType === "youtube"}
          planConfig={planConfig}
          duration={transcription.duration}
          onShare={onShare}
          transcriptionType={transcription.transcriptionType}
          insufficientMinutes={transcription.insufficientMinutes}
          isAnonymous={transcription.isAnonymous}
          transcriptionTime={transcription.transcriptionTime}
          mediaDownloadTime={transcription.mediaDownloadTime}
          mediaProcessingTime={transcription.mediaProcessingTime}
          sourceUrl={transcription.sourceUrl}
          taskStatuses={transcription.taskStatuses}
          requestedServiceProvider={transcription?.requestedServiceProvider}
          actualServiceProvider={transcription?.actualServiceProvider}
        />
      }
      transcriptPanel={
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="mb-4">
              <div className="w-16 h-16 mx-auto mb-4 bg-custom-bg-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-custom-bg-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t("transcriptionNotStarted.title")}
            </h3>
            <p className="text-gray-600 mb-4">
              {t("transcriptionNotStarted.description")}
            </p>
            <button
              onClick={handleStartTranscription}
              disabled={isStartingTranscription}
              className="px-6 py-3 bg-custom-bg-500 text-white rounded-md hover:bg-custom-bg-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 mx-auto"
            >
              {isStartingTranscription && (
                <svg
                  className="animate-spin h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              )}
              {isStartingTranscription
                ? t("transcriptionNotStarted.buttonLoading")
                : t("transcriptionNotStarted.button")}
            </button>
          </div>
        </div>
      }
      overviewPanel={null}
      audioPlayer={null}
    />
  );
};

export default TranscriptionNotStartedView;
