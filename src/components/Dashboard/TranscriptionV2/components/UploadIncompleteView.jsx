"use client";

import { useState, useCallback } from "react";
import { useTranslations } from "next-intl";
import { TranscriptionLayout } from "./Layout";
import { Header } from "./Header";
import { transcriptionService } from "@/services/api/transcriptionService";
import { trackEvent } from "@/lib/analytics";

/**
 * 文件上传未完成状态的视图组件
 * 显示上传失败的提示，并提供返回上传或删除文件的选项
 */
const UploadIncompleteView = ({ transcription, planConfig, onShare }) => {
  const t = useTranslations("transcription");
  const [isDeletingFile, setIsDeletingFile] = useState(false);

  // 删除未完成上传的文件
  const handleDeleteIncompleteFile = useCallback(async () => {
    if (!transcription?.id || isDeletingFile) return;

    try {
      setIsDeletingFile(true);
      await transcriptionService.deleteTranscription(transcription.id);

      // Track successful deletion
      trackEvent("upload_incomplete_delete_success", {});

      // 删除成功后返回上传页面
      window.history.back();
    } catch (error) {
      console.error("Failed to delete incomplete file:", error);

      // Track deletion failure
      trackEvent("upload_incomplete_delete_failed", {
        error: error?.message || "Unknown error",
      });

      // TODO: 显示错误提示
    } finally {
      setIsDeletingFile(false);
    }
  }, [transcription?.id, isDeletingFile]);

  // 返回上传页面
  const handleGoBack = useCallback(() => {
    // Track go back button click
    trackEvent("upload_incomplete_go_back_clicked", {
      sourceType: transcription.sourceType,
    });
    window.history.back();
  }, [transcription?.id, transcription?.filename, transcription?.sourceType]);

  return (
    <TranscriptionLayout
      header={
        <Header
          title={transcription.filename}
          fileId={transcription.id}
          isYouTube={transcription.sourceType === "youtube"}
          planConfig={planConfig}
          duration={transcription.duration}
          onShare={onShare}
          transcriptionType={transcription.transcriptionType}
          insufficientMinutes={transcription.insufficientMinutes}
          isAnonymous={transcription.isAnonymous}
          transcriptionTime={transcription.transcriptionTime}
          mediaDownloadTime={transcription.mediaDownloadTime}
          mediaProcessingTime={transcription.mediaProcessingTime}
          sourceUrl={transcription.sourceUrl}
          taskStatuses={transcription.taskStatuses}
          requestedServiceProvider={transcription?.requestedServiceProvider}
          actualServiceProvider={transcription?.actualServiceProvider}
        />
      }
      transcriptPanel={
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="mb-4">
              <div className="w-16 h-16 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-yellow-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t("uploadIncomplete.title")}
            </h3>
            <p className="text-gray-600 mb-4">
              {t("uploadIncomplete.description")}
            </p>
            <div className="flex gap-3 justify-center">
              <button
                onClick={handleGoBack}
                className="px-4 py-2 bg-custom-bg-500 text-white rounded-md hover:bg-custom-bg-600"
              >
                {t("uploadIncomplete.button")}
              </button>
              <button
                onClick={() => {
                  if (window.confirm(t("uploadIncomplete.deleteConfirm"))) {
                    handleDeleteIncompleteFile();
                  }
                }}
                disabled={isDeletingFile}
                className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isDeletingFile && (
                  <svg
                    className="animate-spin h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                )}
                {isDeletingFile
                  ? t("uploadIncomplete.deleteButtonLoading")
                  : t("uploadIncomplete.deleteButton")}
              </button>
            </div>
          </div>
        </div>
      }
      overviewPanel={null}
      audioPlayer={null}
    />
  );
};

export default UploadIncompleteView;
