"use client";

import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Sidebar } from "../../DashboardV2/components/Sidebar";
import { SidebarSkeleton } from "../../DashboardV2/components/SidebarSkeleton";
import supabase from "@/lib/supabaseClient";
import GlobalUpgradeDialog from "@/components/GlobalUpgradeDialog";

function cloneIfComponent(element, props) {
  return React.isValidElement(element)
    ? React.cloneElement(element, typeof element.type === "string" ? {} : props)
    : element;
}

export function TranscriptionLayout({
  header,
  transcriptPanel,
  overviewPanel,
  audioPlayer,
  className,
}) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isAnonymous, setIsAnonymous] = useState(null); // null 表示未知状态
  const [isSessionLoading, setIsSessionLoading] = useState(true); // 会话加载状态

  useEffect(() => {
    const checkSession = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        setIsAnonymous(session?.user?.is_anonymous || false);
      } catch (error) {
        console.error("Error checking session:", error);
        setIsAnonymous(false); // 出错时默认为非匿名用户
      } finally {
        setIsSessionLoading(false);
      }
    };

    checkSession();
  }, []);

  return (
    <div className="flex h-screen overflow-hidden bg-white">
      {/* Sidebar - 加载状态显示骨架屏，否则根据用户状态显示相应版本 */}
      {isSessionLoading ? (
        <SidebarSkeleton className="hidden md:flex h-screen flex-shrink-0" />
      ) : (
        <Sidebar
          className="hidden md:flex h-screen flex-shrink-0"
          isAnonymous={isAnonymous}
        />
      )}

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Header */}
        <div className="flex-none h-16 border-b border-gray-200">
          {cloneIfComponent(header, { isAnonymous })}
        </div>

        {/* 内容区域 */}
        <div
          className={cn(
            "flex flex-col md:flex-row flex-1 min-h-0 overflow-hidden",
            className
          )}
        >
          {/* 左侧：转录面板 + 播放器 */}
          <div className="w-full md:w-1/2 border-r border-gray-200 flex flex-col min-h-0">
            {/* 转录面板 */}
            <div className="flex-1 min-h-0">
              {cloneIfComponent(transcriptPanel, { isAnonymous })}
            </div>
            {/* 播放器 - 匿名用户也显示 */}
            <div className="flex-none h-24 fixed md:relative bottom-0 left-0 right-0 md:bottom-auto bg-gray-50 z-10">
              {audioPlayer}
            </div>
          </div>

          {/* 右侧概览面板 */}
          <div
            className={cn(
              "w-[90%] md:w-1/2 h-full",
              "fixed right-0 top-16 bottom-24 md:relative md:top-0 md:bottom-0 md:right-0",
              "bg-white md:rounded-none",
              "shadow-lg md:shadow-none transition-transform duration-300",
              "md:translate-x-0 z-20",
              isDrawerOpen ? "translate-x-0" : "translate-x-full"
            )}
          >
            {/* 抽屉把手 */}
            <div
              className="absolute left-0 top-1/3 -translate-x-full -translate-y-1/2 w-6 h-16 flex items-center justify-center md:hidden cursor-pointer bg-white rounded-l-lg shadow-lg"
              onClick={() => setIsDrawerOpen(!isDrawerOpen)}
            >
              <div className="w-0.5 h-8 bg-gray-300 rounded-full" />
            </div>
            {/* 概览面板内容 */}
            <div className="h-full overflow-y-auto">
              {cloneIfComponent(overviewPanel, { isAnonymous })}
            </div>
          </div>
        </div>
      </div>

      {/* Global UpgradeDialog */}
      <GlobalUpgradeDialog />
    </div>
  );
}
