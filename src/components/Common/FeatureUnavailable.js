"use client";

import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { useRouter } from "@/i18n/navigation";

const FeatureUnavailable = () => {
  const t = useTranslations("common.featureUnavailable");
  const router = useRouter();

  const handleGoHome = () => {
    router.push("/");
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center px-4 pt-20 pb-20">
      <div className="max-w-md w-full text-center mx-auto -mt-32">
        {/* Robot Icon */}
        <div className="mb-8 flex justify-center">
          <svg
            width="120"
            height="120"
            viewBox="0 0 120 120"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-gray-400"
          >
            {/* Robot Head */}
            <rect
              x="30"
              y="35"
              width="60"
              height="50"
              rx="8"
              fill="currentColor"
              opacity="0.8"
            />

            {/* Antenna */}
            <circle cx="60" cy="25" r="3" fill="currentColor" opacity="0.6" />
            <line
              x1="60"
              y1="28"
              x2="60"
              y2="35"
              stroke="currentColor"
              strokeWidth="2"
              opacity="0.6"
            />

            {/* Eyes (X marks) */}
            <g opacity="0.9">
              <line
                x1="45"
                y1="50"
                x2="52"
                y2="57"
                stroke="white"
                strokeWidth="3"
                strokeLinecap="round"
              />
              <line
                x1="52"
                y1="50"
                x2="45"
                y2="57"
                stroke="white"
                strokeWidth="3"
                strokeLinecap="round"
              />
              <line
                x1="68"
                y1="50"
                x2="75"
                y2="57"
                stroke="white"
                strokeWidth="3"
                strokeLinecap="round"
              />
              <line
                x1="75"
                y1="50"
                x2="68"
                y2="57"
                stroke="white"
                strokeWidth="3"
                strokeLinecap="round"
              />
            </g>

            {/* Robot Body */}
            <rect
              x="35"
              y="85"
              width="50"
              height="25"
              rx="6"
              fill="currentColor"
              opacity="0.7"
            />

            {/* Arms */}
            <rect
              x="20"
              y="90"
              width="15"
              height="8"
              rx="4"
              fill="currentColor"
              opacity="0.6"
            />
            <rect
              x="85"
              y="90"
              width="15"
              height="8"
              rx="4"
              fill="currentColor"
              opacity="0.6"
            />

            {/* Lock Icon */}
            <g transform="translate(95, 75)">
              <rect
                x="0"
                y="8"
                width="20"
                height="15"
                rx="3"
                fill="currentColor"
                opacity="0.8"
              />
              <path
                d="M5 8V5C5 2.24 7.24 0 10 0C12.76 0 15 2.24 15 5V8"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                opacity="0.8"
              />
              <circle cx="10" cy="15" r="2" fill="white" opacity="0.9" />
            </g>
          </svg>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">{t("title")}</h1>

        {/* Description */}
        <div className="text-gray-600 mb-8">
          <p>{t("description")}</p>
        </div>

        {/* Action Button */}
        <div className="flex justify-center">
          <Button
            onClick={handleGoHome}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            {t("buttons.goHome")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FeatureUnavailable;
