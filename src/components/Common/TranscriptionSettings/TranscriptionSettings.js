import { Switch } from "@/components/ui/switch";
import {
  Too<PERSON>ip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import {
  Info,
  ChevronDown,
  ChevronUp,
  AlertTriangle,
  Crown,
} from "lucide-react";
import { useTranslations } from "next-intl";
import LanguageSelector from "@/components/Common/FileUploader/LanguageSelector";
import PremiumFeatureDialog from "@/components/Common/PremiumFeatureDialog";

/**
 * 转录设置通用组件
 * 提供语言选择、字幕生成、说话人识别等设置功能
 */
const TranscriptionSettings = ({
  // 设置状态
  selectedLanguage,
  subtitleEnabled,
  enableSpeakerDiarization,
  advancedSettingsOpen,
  showPremiumDialog,

  // 事件处理函数
  onLanguageSelect,
  onSubtitleChange,
  onSpeakerDiarizationChange,
  onAdvancedSettingsToggle,
  onPremiumDialogClose,

  // 显示配置
  showLanguageSelector = true,
  showSubtitleToggle = true,
  showAdvancedSettings = true,
  showSpeakerDiarization = true,
  showWarning = true,

  // 布局配置
  layout = "vertical", // "vertical" | "horizontal" | "compact"
  containerClassName = "",
  spacing = "normal", // "compact" | "normal" | "relaxed"

  // Premium Dialog 配置
  premiumDialogSource = "transcription_settings",
}) => {
  const tCommon = useTranslations("common");

  // 根据spacing计算间距类名
  const getSpacingClass = () => {
    switch (spacing) {
      case "compact":
        return "space-y-2";
      case "relaxed":
        return "space-y-6";
      default:
        return "space-y-4";
    }
  };

  // 根据layout计算容器类名
  const getLayoutClass = () => {
    switch (layout) {
      case "horizontal":
        return "flex flex-wrap gap-4 items-start";
      case "compact":
        return "space-y-2";
      default:
        return getSpacingClass();
    }
  };

  return (
    <div className={`${getLayoutClass()} ${containerClassName}`}>
      {/* 语言选择器 */}
      {showLanguageSelector && (
        <div className={layout === "horizontal" ? "flex-shrink-0" : ""}>
          <LanguageSelector
            selectedLanguage={selectedLanguage}
            onLanguageSelect={onLanguageSelect}
          />
        </div>
      )}

      {/* 高级设置 */}
      {showAdvancedSettings && (
        <div className={layout === "horizontal" ? "flex-1 min-w-0" : ""}>
          <div className="space-y-3">
            <Button
              variant="ghost"
              className="flex items-center justify-between w-full p-0 h-auto text-sm font-medium text-gray-700 hover:bg-transparent"
              onClick={onAdvancedSettingsToggle}
            >
              <span>{tCommon("fileDisplay.advancedSettings.title")}</span>
              {advancedSettingsOpen ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>

            {advancedSettingsOpen && (
              <div className="space-y-4 pl-4 bg-gray-50/50 rounded-md p-3">
                {/* 字幕生成开关 */}
                {showSubtitleToggle && (
                  <div className="flex items-center justify-between gap-4">
                    <label className="text-sm font-medium text-gray-700 whitespace-nowrap flex items-center">
                      {tCommon("fileDisplay.subtitle.generate_subtitle")}
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 ml-1 text-gray-400" />
                          </TooltipTrigger>
                          <TooltipContent
                            className="max-w-xs z-[9999]"
                            side="top"
                            align="center"
                            sideOffset={8}
                          >
                            <p className="text-sm whitespace-normal leading-relaxed">
                              {tCommon("fileDisplay.subtitle.subtitle_tooltip")}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </label>
                    <Switch
                      checked={subtitleEnabled}
                      onCheckedChange={onSubtitleChange}
                      className="data-[state=checked]:bg-custom-bg"
                    />
                  </div>
                )}

                {/* Speaker Recognition */}
                {showSpeakerDiarization && (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between gap-4">
                      <label className="text-sm font-medium text-gray-700 whitespace-nowrap flex items-center">
                        <Crown className="h-4 w-4 mr-2 text-yellow-500" />
                        {tCommon("fileDisplay.speakerRecognition.title")}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Info className="h-4 w-4 ml-1 text-gray-400" />
                            </TooltipTrigger>
                            <TooltipContent
                              className="max-w-xs z-[9999]"
                              side="top"
                              align="center"
                              sideOffset={8}
                            >
                              <p className="text-sm whitespace-normal leading-relaxed">
                                {tCommon(
                                  "fileDisplay.speakerRecognition.tooltip"
                                )}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </label>
                      <Switch
                        checked={enableSpeakerDiarization}
                        onCheckedChange={onSpeakerDiarizationChange}
                        className="data-[state=checked]:bg-custom-bg"
                      />
                    </div>

                    {/* Warning when both subtitle and speaker recognition are enabled */}
                    {showWarning &&
                      subtitleEnabled &&
                      enableSpeakerDiarization && (
                        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                          <div className="flex items-start space-x-2">
                            <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 shrink-0" />
                            <div className="text-sm text-yellow-800">
                              <p className="font-medium mb-1">
                                {tCommon(
                                  "fileDisplay.warning.subtitle_quality_title"
                                )}
                              </p>
                              <p>
                                {tCommon(
                                  "fileDisplay.warning.subtitle_quality_description"
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Premium功能提示Dialog */}
      {showPremiumDialog && (
        <PremiumFeatureDialog
          isOpen={showPremiumDialog}
          onClose={onPremiumDialogClose}
          source={premiumDialogSource}
        />
      )}
    </div>
  );
};

export default TranscriptionSettings;
