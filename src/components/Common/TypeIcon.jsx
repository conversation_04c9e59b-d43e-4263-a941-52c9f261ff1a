import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";

/**
 * TypeIcon 组件 - 显示转录类型图标
 * @param {string} type - 转录类型 ("transcript" 或 "subtitle")
 * @param {string} className - 额外的 CSS 类名
 */
export const TypeIcon = ({ type, className = "" }) => {
  // 根据类型确定显示的字母和提示文本
  const getTypeConfig = (type) => {
    const normalizedType = type?.toLowerCase();
    
    switch (normalizedType) {
      case "transcript":
        return {
          letter: "T",
          tooltip: "Transcript",
        };
      case "subtitle":
        return {
          letter: "S", 
          tooltip: "Subtitle",
        };
      default:
        return {
          letter: "T",
          tooltip: "Transcript",
        };
    }
  };

  const { letter, tooltip } = getTypeConfig(type);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={`
              w-6 h-6 
              rounded-full 
              bg-muted/50 
              hover:bg-muted 
              flex items-center justify-center 
              text-xs font-medium 
              text-muted-foreground 
              cursor-help 
              transition-colors
              ${className}
            `}
          >
            {letter}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
