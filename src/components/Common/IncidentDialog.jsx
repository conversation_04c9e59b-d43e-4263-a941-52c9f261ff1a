"use client";

import { useState, useEffect } from "react";
import { AlertCircle, CheckCircle, Clock, ExternalLink } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FEATURES } from "@/config/features";
import { format, parse } from "date-fns";

const IncidentDialog = () => {
  const [open, setOpen] = useState(false);
  const {
    enabled,
    severity,
    title,
    message,
    incidentTime,
    resolvedTime,
    statusUrl,
    updates,
    affectedServices,
    unaffectedServices,
  } = FEATURES.INCIDENT_NOTICE;

  // 格式化日期时间
  const formatDateTime = (dateTimeStr) => {
    if (!dateTimeStr) return "";
    try {
      const date = parse(dateTimeStr, "yyyy-MM-dd HH:mm", new Date());
      return format(date, "MMM d, yyyy 'at' HH:mm 'UTC'");
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateTimeStr;
    }
  };

  // 获取严重程度的样式和图标
  const getSeverityConfig = (severity) => {
    switch (severity) {
      case "critical":
        return {
          color: "bg-red-100 text-red-800 border-red-200",
          icon: <AlertCircle className="h-4 w-4" />,
          label: "Critical",
        };
      case "major":
        return {
          color: "bg-orange-100 text-orange-800 border-orange-200",
          icon: <AlertCircle className="h-4 w-4" />,
          label: "Major",
        };
      case "minor":
        return {
          color: "bg-yellow-100 text-yellow-800 border-yellow-200",
          icon: <Clock className="h-4 w-4" />,
          label: "Minor",
        };
      case "resolved":
        return {
          color: "bg-green-100 text-green-800 border-green-200",
          icon: <CheckCircle className="h-4 w-4" />,
          label: "Resolved",
        };
      default:
        return {
          color: "bg-gray-100 text-gray-800 border-gray-200",
          icon: <AlertCircle className="h-4 w-4" />,
          label: "Unknown",
        };
    }
  };

  const severityConfig = getSeverityConfig(severity);

  // 检查本地存储，看用户是否已经关闭过对话框
  useEffect(() => {
    if (!enabled) return;

    const incidentKey = `incident_${incidentTime}_dismissed`;
    const incidentDismissed = localStorage.getItem(incidentKey);

    if (!incidentDismissed) {
      setOpen(true);
    }
  }, [enabled, incidentTime]);

  const handleClose = () => {
    setOpen(false);
    // 使用事故时间作为唯一标识符
    const incidentKey = `incident_${incidentTime}_dismissed`;
    localStorage.setItem(incidentKey, Date.now().toString());
  };

  const handleStatusPageClick = () => {
    if (statusUrl) {
      window.open(statusUrl, "_blank", "noopener,noreferrer");
    }
  };

  if (!enabled) return null;

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          handleClose();
        }
      }}
    >
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {severityConfig.icon}
            {title || "Service Incident"}
          </DialogTitle>
          <div className="flex items-center gap-2 mt-2">
            <Badge className={`${severityConfig.color} border`}>
              {severityConfig.label}
            </Badge>
            {resolvedTime && (
              <span className="text-sm text-gray-500">
                Resolved at {formatDateTime(resolvedTime)}
              </span>
            )}
          </div>
          <DialogDescription className="mt-2">{message}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Incident Timeline */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-sm mb-2">Incident Started</h4>
            <p className="text-sm text-gray-600">
              {formatDateTime(incidentTime)}
            </p>
          </div>

          {/* Status Updates */}
          {updates && updates.length > 0 && (
            <div>
              <h4 className="font-medium text-sm mb-3">Status Updates</h4>
              <div className="space-y-3">
                {updates.map((update, index) => (
                  <div key={index} className="border-l-2 border-gray-200 pl-4">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className="text-xs">
                        {update.status}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {formatDateTime(update.time)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700">{update.message}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Affected Services */}
          {affectedServices && affectedServices.length > 0 && (
            <div>
              <h4 className="font-medium text-sm mb-2">Affected Services</h4>
              <ul className="list-disc pl-5 space-y-1 text-sm text-gray-600">
                {affectedServices.map((service, index) => (
                  <li key={index}>{service}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Unaffected Services */}
          {unaffectedServices && unaffectedServices.length > 0 && (
            <div>
              <h4 className="font-medium text-sm mb-2">
                Services Operating Normally
              </h4>
              <ul className="list-disc pl-5 space-y-1 text-sm text-green-600">
                {unaffectedServices.map((service, index) => (
                  <li key={index}>{service}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Status Page Link */}
          {statusUrl && (
            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800 mb-2">
                For real-time updates and detailed information:
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={handleStatusPageClick}
                className="text-blue-600 border-blue-300 hover:bg-blue-100"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                View Status Page
              </Button>
            </div>
          )}
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button
            onClick={handleClose}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Got it
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IncidentDialog;
