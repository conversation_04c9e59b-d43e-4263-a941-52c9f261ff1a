"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Crown } from "lucide-react";
import { useTranslations } from "next-intl";
import { trackEvent } from "@/lib/analytics";
import { useAuthStore } from "@/stores/useAuthStore";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";

const PremiumFeatureDialog = ({
  isOpen,
  onClose,
  featureName = "Speaker Recognition",
  source = "premium_feature_dialog",
}) => {
  const t = useTranslations("common.premiumFeatureDialog");
  const { user } = useAuthStore();
  const { openDialog } = useUpgradeDialogStore();

  // 检查是否为匿名用户
  const isAnonymousUser = user?.isAnonymous;

  const handleUpgradeClick = () => {
    // 跟踪事件
    trackEvent("premium_feature_upgrade_click", {
      feature: featureName,
      source: source,
      isAnonymous: isAnonymousUser,
    });

    openDialog({
      source: `${source}_upgrade`,
      defaultPlanType: "yearly",
      title: "",
      description: "",
    });

    // 当升级dialog打开时，关闭premium feature dialog
    onClose();
  };

  return (
    <>
      {/* Premium Feature Dialog - 当升级dialog显示时隐藏 */}
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl">
              <Crown className="h-6 w-6 text-indigo-500" />
              {t("title")}
            </DialogTitle>
          </DialogHeader>

          <div className="py-4">
            <div className="flex items-center justify-center mb-4">
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-full flex items-center justify-center">
                  <Crown className="h-8 w-8 text-white" />
                </div>
              </div>
            </div>

            <div className="text-center space-y-3">
              <h3 className="text-lg font-semibold text-gray-900">
                {t("featureTitle", { feature: featureName })}
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                {t("description")}
              </p>
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button variant="outline" onClick={onClose} className="flex-1">
              {t("cancel")}
            </Button>
            <Button
              onClick={handleUpgradeClick}
              className="flex-1 bg-gradient-to-r from-indigo-500 to-purple-500  hover:from-indigo-600 hover:to-purple-600 text-white border-0"
            >
              <Crown className="h-4 w-4 mr-2" />
              {t("upgrade")}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default PremiumFeatureDialog;
