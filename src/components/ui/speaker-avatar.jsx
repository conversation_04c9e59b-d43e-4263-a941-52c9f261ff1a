"use client";

import { getSpeakerColor, getSpeakerInitials } from "@/lib/utils";
import { CircleUserRound } from "lucide-react";

/**
 * Speaker Avatar 组件 - 为不同的 speaker 显示不同颜色的头像
 * @param {string} speakerName - speaker 名称
 * @param {string} size - 头像大小 ("sm", "md", "lg")
 * @param {string} variant - 头像样式 ("colored", "icon")
 * @param {string} className - 额外的 CSS 类名
 */
export function SpeakerAvatar({
  speakerName,
  size = "md",
  variant = "colored",
  className = "",
}) {
  const sizeClasses = {
    sm: "w-5 h-5 text-xs",
    md: "w-6 h-6 text-sm",
    lg: "w-8 h-8 text-base",
  };

  // 防护处理：确保 speakerName 有效
  const safeSpeakerName = speakerName || "Unknown Speaker";

  // 如果是 icon 变体，使用原来的图标样式
  if (variant === "icon") {
    return (
      <CircleUserRound
        className={`${sizeClasses[size]} text-custom-bg ${className}`}
      />
    );
  }

  // 彩色头像变体
  const colors = getSpeakerColor(safeSpeakerName);
  const initials = getSpeakerInitials(safeSpeakerName);

  return (
    <div
      className={`
        ${sizeClasses[size]}
        ${colors.bg}
        ${colors.text}
        rounded-full
        flex
        items-center
        justify-center
        font-medium
        border-2
        ${colors.border}
        shadow-sm
        ${className}
      `}
      title={safeSpeakerName}
    >
      {initials}
    </div>
  );
}

export default SpeakerAvatar;
