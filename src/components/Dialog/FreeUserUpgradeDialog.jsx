"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Check, X, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { subscriptionService } from "@/services/api/subscriptionService";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import { trackEvent } from "@/lib/analytics";
import { safeOpenUrl } from "@/lib/browserUtils";
import { FEATURES } from "@/config/features";

const FreeUserUpgradeDialog = ({ isOpen, onOpenChange, limitType }) => {
  const t = useTranslations("dashboard.freeUserUpgradeDialog");
  const [isUpgrading, setIsUpgrading] = useState(false);
  const { openDialog } = useUpgradeDialogStore();

  // 从FEATURES配置中获取促销信息，与PromotionCard保持一致
  const { plan, promoCode } = FEATURES.PROMO_CARD || {
    plan: "basic_yearly",
    promoCode: "BASIC50",
  };

  const getDialogContent = () => {
    switch (limitType) {
      case "minutes":
        return {
          description: t("minutes.description"),
        };
      case "daily_count":
        return {
          description: t("daily_count.description"),
        };
      default:
        return {
          description: t("default.description"),
        };
    }
  };

  const content = getDialogContent();

  // 统一的关闭处理函数，包含事件追踪
  const handleClose = (closeMethod = "unknown") => {
    // 跟踪关闭事件
    trackEvent("free_user_upgrade_dialog_close", {
      limitType: limitType,
      source: "free_user_upgrade_dialog",
      closeMethod: closeMethod, // "close_button", "escape_key", "upgrade_success", "see_all_plans"
    });

    onOpenChange(false);
  };

  // 添加键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape" && isOpen) {
        handleClose("escape_key");
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      // 防止背景滚动
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onOpenChange]);

  const handleUpgrade = async () => {
    setIsUpgrading(true);
    try {
      const response = await subscriptionService.createCheckoutSession(
        plan,
        promoCode,
        "subscription",
        "free_user_upgrade_dialog"
      );

      const url = response.data.url;
      if (url) {
        safeOpenUrl(url);
        handleClose("upgrade_success");
      }
    } catch (error) {
      console.error("Failed to create checkout session:", error);

      // 跟踪升级失败事件
      trackEvent("free_user_upgrade_failed", {
        limitType: limitType,
        source: "free_user_upgrade_dialog",
        error: error.message || "Unknown error",
      });

      // 可以在这里添加用户友好的错误提示
      // 例如显示一个toast或者错误消息
    } finally {
      setIsUpgrading(false);
    }
  };

  const handleSeeAllPlans = () => {
    openDialog({
      source: "free_user_upgrade_dialog_see_all_plans",
      defaultPlanType: "yearly",
      title: "",
      description: "",
    });

    handleClose("see_all_plans");
  };

  // 移除点击背景关闭功能 - 用户需要明确选择操作

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/30 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-lg bg-white border-0 shadow-2xl max-h-[90vh] overflow-y-auto">
        <CardContent className="p-6 text-center">
          {/* 关闭按钮 */}
          <div className="flex justify-end mb-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleClose("close_button")}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* 顶部：警示图标、标题、描述 */}
          <div className="w-16 h-16 bg-marketing-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-8 h-8 text-marketing-600" />
          </div>
          <h2 className="text-2xl font-bold text-marketing-900 mb-2">
            {t("title")}
          </h2>
          <p className="text-marketing-700 mb-6">{content.description}</p>

          {/* 中部：功能列表和价格展示 */}
          <div className="bg-gradient-to-r from-marketing-50 to-marketing-100 rounded-lg p-4 mb-5">
            <h3 className="text-lg font-bold text-marketing-900 mb-3">
              {t("planTitle")}
            </h3>
            <div className="space-y-2 text-left text-sm">
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 bg-marketing-200 rounded-full flex items-center justify-center flex-shrink-0">
                  <Check className="w-3 h-3 text-marketing-700" />
                </div>
                <span className="text-marketing-800 font-medium">
                  {t("features.minutes")}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 bg-marketing-200 rounded-full flex items-center justify-center flex-shrink-0">
                  <Check className="w-3 h-3 text-marketing-700" />
                </div>
                <span className="text-marketing-800 font-medium">
                  {t("features.noLimit")}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 bg-marketing-200 rounded-full flex items-center justify-center flex-shrink-0">
                  <Check className="w-3 h-3 text-marketing-700" />
                </div>
                <span className="text-marketing-800 font-medium">
                  {t("features.noRetention")}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 bg-marketing-200 rounded-full flex items-center justify-center flex-shrink-0">
                  <Check className="w-3 h-3 text-marketing-700" />
                </div>
                <span className="text-marketing-800 font-medium">
                  {t("features.aiFeatures")}
                </span>
              </div>
            </div>
          </div>

          {/* 价格展示区域 */}
          <div className="mb-5">
            <div className="bg-red-50 border border-red-200 rounded-lg p-2 mb-3">
              <p className="text-red-700 text-sm font-medium">
                ⚡ {t("pricing.discount")}
              </p>
            </div>
            <div className="flex items-center justify-center gap-2 mb-1">
              <span className="text-lg text-gray-400 line-through">
                {t("pricing.originalPrice")}
              </span>
              <span className="text-3xl font-bold text-marketing-600">
                {t("pricing.currentPrice")}
              </span>
              <span className="text-base text-gray-600">
                {t("pricing.perYear")}
              </span>
            </div>
            <p className="text-marketing-700 font-medium text-sm">
              {t("pricing.perMonth")}
            </p>
          </div>

          {/* 底部：升级按钮、查看pricing按钮、信任提示 */}
          <div className="mb-4">
            <Button
              className="w-full py-3 bg-marketing-600 hover:bg-marketing-700 text-white font-bold shadow-lg mb-3 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleUpgrade}
              disabled={isUpgrading}
            >
              {isUpgrading ? (
                <div className="flex items-center justify-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  {t("upgrading")}
                </div>
              ) : (
                t("upgradeButton")
              )}
            </Button>
            <Button
              variant="outline"
              className="w-full py-2.5 border-marketing-300 text-marketing-700 hover:bg-marketing-50 bg-transparent font-medium"
              onClick={handleSeeAllPlans}
            >
              {t("seeAllPlans")}
            </Button>
          </div>

          {/* 信任提示区域 */}
          <div className="flex items-center justify-center gap-4 text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>{t("trust.cancelAnytime")}</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-marketing-500 rounded-full"></div>
              <span>{t("trust.instantAccess")}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FreeUserUpgradeDialog;
