/**
 * 上传配置文件
 *
 * 统一的文件上传配置，使用 Uppy 作为上传引擎
 */

import { SUPPORTED_AUDIO_FORMATS } from "@/constants/file";

// 上传配置
export const UPLOAD_CONFIG = {
  // 最大文件大小 (5GB)
  maxFileSize: 5 * 1024 * 1024 * 1024,
  minFileSize: 1024, // 1KB

  // 支持的文件类型 - 基于 SUPPORTED_AUDIO_FORMATS 常量
  allowedFileTypes: SUPPORTED_AUDIO_FORMATS.map((format) => `.${format}`),

  // 上传超时时间 (60分钟)
  uploadTimeout: 60 * 60 * 1000,

  // 是否启用调试模式
  debug: process.env.NODE_ENV === "development",

  // 分块上传配置
  multipart: {
    // 分块上传阈值 - 基于 R2 限制：最小分块大小 5MiB
    // 小于此阈值的文件无法使用 multipart，必须用单次上传
    threshold: 100 * 1024 * 1024, // 100MiB (104,857,600 bytes)

    // 分块大小 - R2 要求：最小 5MiB，最大 5GiB
    // 设置为 10MiB 平衡性能和内存使用
    chunkSize: 10 * 1024 * 1024, // 10MiB (10,485,760 bytes)

    // 重试延迟配置 (毫秒)
    retryDelays: [0, 1000, 3000, 5000],

    // 最大重试次数
    maxRetries: 3,

    // 并发上传分块数量
    limit: 3,
  },
};
