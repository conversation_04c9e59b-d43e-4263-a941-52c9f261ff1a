import { create } from 'zustand';

export const useUpgradeDialogStore = create((set) => ({
  isOpen: false,
  source: 'unknown',
  defaultPlanType: 'yearly',
  title: '',
  description: '',
  showFreeTier: false,
  
  openDialog: (options = {}) => set({
    isOpen: true,
    source: options.source || 'unknown',
    defaultPlanType: options.defaultPlanType || 'yearly',
    title: options.title || '',
    description: options.description || '',
    showFreeTier: options.showFreeTier || false,
  }),
  
  closeDialog: () => set({
    isOpen: false,
  }),
}));
