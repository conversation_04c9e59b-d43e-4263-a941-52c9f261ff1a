import axiosInstance from "./axiosInstance";
import {
  GET_API_KEYS,
  CREATE_API_KEY,
  UPDATE_API_KEY,
  RESET_API_KEY,
  DELETE_API_KEY,
} from "@/constants/endpoints";

export const apiKeyService = {
  /**
   * Get all API keys for the authenticated user
   * @returns {Promise<Object>} Response with API keys list
   */
  getApiKeys: async () => {
    const response = await axiosInstance.get(GET_API_KEYS);
    return response;
  },

  /**
   * Create a new API key
   * @param {Object} keyData - API key creation data
   * @param {string} keyData.name - User-friendly name for the key (1-100 characters)
   * @param {number} [keyData.expiresDays] - Number of days until expiration (1-3650, null for no expiration)
   * @returns {Promise<Object>} Response with created API key (includes full key)
   */
  createApiKey: async (keyData) => {
    const response = await axiosInstance.post(CREATE_API_KEY, keyData);
    return response;
  },

  /**
   * Update an existing API key (name only)
   * @param {string} keyId - API key ID
   * @param {Object} updateData - Update data
   * @param {string} updateData.name - New name for the API key (1-100 characters)
   * @returns {Promise<Object>} Response with updated API key
   */
  updateApiKey: async (keyId, updateData) => {
    const response = await axiosInstance.put(UPDATE_API_KEY(keyId), updateData);
    return response;
  },

  /**
   * Reset an existing API key (generates new key value)
   * @param {string} keyId - API key ID
   * @returns {Promise<Object>} Response with new API key (includes full key)
   */
  resetApiKey: async (keyId) => {
    const response = await axiosInstance.post(RESET_API_KEY(keyId));
    return response;
  },

  /**
   * Delete an API key (soft delete - marks as inactive)
   * @param {string} keyId - API key ID
   * @returns {Promise<Object>} Response confirming deletion
   */
  deleteApiKey: async (keyId) => {
    const response = await axiosInstance.delete(DELETE_API_KEY(keyId));
    return response;
  },
};
