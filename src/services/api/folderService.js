import axiosInstance from "./axiosInstance";
import {
  GET_FOLDERS,
  CREATE_FOLDER,
  UPDATE_FOLDER,
  DELETE_FOLDER,
  GET_FOLDER_TRANSCRIPTIONS,
} from "@/constants/endpoints";

export const folderService = {
  /**
   * 获取所有文件夹
   * @returns {Promise} API响应
   */
  getFolders: async () => {
    const response = await axiosInstance.get(GET_FOLDERS);
    return response;
  },

  /**
   * 创建新文件夹
   * @param {string} name - 文件夹名称
   * @returns {Promise} API响应
   */
  createFolder: async (name) => {
    const response = await axiosInstance.post(CREATE_FOLDER, {
      name,
    });
    return response;
  },

  /**
   * 修改文件夹名称
   * @param {string} folderId - 文件夹ID
   * @param {string} name - 新的文件夹名称
   * @returns {Promise} API响应
   */
  updateFolder: async (folderId, name) => {
    const response = await axiosInstance.patch(UPDATE_FOLDER(folderId), {
      name,
    });
    return response;
  },

  /**
   * 删除文件夹
   * @param {string} folderId - 文件夹ID
   * @returns {Promise} API响应
   */
  deleteFolder: async (folderId) => {
    const response = await axiosInstance.delete(DELETE_FOLDER(folderId));
    return response;
  },

  /**
   * 获取文件夹中的转录记录
   * @param {string} folderId - 文件夹ID
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @returns {Promise} API响应
   */
  getFolderTranscriptions: async (folderId, page = 1, pageSize = 10) => {
    const response = await axiosInstance.get(
      GET_FOLDER_TRANSCRIPTIONS(folderId),
      {
        params: {
          page,
          pageSize,
        },
      }
    );
    return response;
  },
};
