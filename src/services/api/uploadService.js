import axiosInstance from "./axiosInstance";
import { GENERATE_SIGNED_URL, COMPLETE_UPLOAD } from "@/constants/endpoints";
import { getFileExtension } from "@/lib/fileUtils";
import { isInvalidDuration } from "@/lib/utils";

export const uploadService = {
  generateSignedUrl: async (
    file,
    md5HashBase64,
    fileDuration,
    forceUpload = false,
    languageCode,
    subtitleEnabled = false,
    folderId = null
  ) => {
    const fileType = getFileExtension(file);
    const fileSize = file.size;
    const filename = file.name.includes(".")
      ? file.name.substring(0, file.name.lastIndexOf("."))
      : file.name;
    const transcriptionType = subtitleEnabled ? "subtitle" : "transcript";

    const requestData = {
      filename: filename,
      fileType: fileType,
      fileSize: fileSize,
      contentMd5Base64: md5HashBase64,
      duration: fileDuration,
      forceUpload,
      languageCode,
      transcriptionType,
    };

    // Add needsPreprocessing parameter when fileDuration is invalid
    if (isInvalidDuration(fileDuration)) {
      requestData.needsPreprocessing = true;
    }

    // 只有当folderId不为null且不为"all"且不为"unclassified"时才添加到请求中
    if (folderId && folderId !== "all" && folderId !== "unclassified") {
      requestData.folderId = String(folderId);
    }

    const response = await axiosInstance.post(GENERATE_SIGNED_URL, requestData);
    return response;
  },

  completeUpload: async (transcriptionFileId) => {
    const response = await axiosInstance.post(COMPLETE_UPLOAD, {
      transcriptionFileId,
    });
    return response;
  },

  // Multipart Upload 相关方法
  createMultipartUpload: async (
    file,
    md5HashBase64,
    fileDuration,
    forceUpload = false,
    languageCode,
    subtitleEnabled = false,
    folderId = null
  ) => {
    const fileType = getFileExtension(file);
    const fileSize = file.size;
    const filename = file.name.includes(".")
      ? file.name.substring(0, file.name.lastIndexOf("."))
      : file.name;
    const transcriptionType = subtitleEnabled ? "subtitle" : "transcript";

    const requestData = {
      filename: filename,
      fileType: fileType,
      fileSize: fileSize,
      contentMd5Base64: md5HashBase64,
      duration: fileDuration,
      forceUpload,
      languageCode,
      transcriptionType,
    };

    // Add needsPreprocessing parameter when fileDuration is invalid
    if (isInvalidDuration(fileDuration)) {
      requestData.needsPreprocessing = true;
    }

    // 只有当folderId不为null且不为"all"且不为"unclassified"时才添加到请求中
    if (folderId && folderId !== "all" && folderId !== "unclassified") {
      requestData.folderId = String(folderId);
    }

    const response = await axiosInstance.post(
      "/upload/multipart/create",
      requestData
    );
    return response;
  },

  listMultipartParts: async (uploadId) => {
    const response = await axiosInstance.get(
      `/upload/multipart/${uploadId}/parts`
    );
    return response;
  },

  signMultipartPart: async (uploadId, partNumber) => {
    const response = await axiosInstance.post(
      `/upload/multipart/${uploadId}/part/${partNumber}/sign`
    );
    return response;
  },

  completeMultipartUpload: async (uploadId, parts) => {
    const response = await axiosInstance.post(
      `/upload/multipart/${uploadId}/complete`,
      { parts }
    );
    return response;
  },

  abortMultipartUpload: async (uploadId) => {
    const response = await axiosInstance.post(
      `/upload/multipart/${uploadId}/abort`
    );
    return response;
  },
};
