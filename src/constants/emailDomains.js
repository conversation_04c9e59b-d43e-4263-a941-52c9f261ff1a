/**
 * Email domains that may reject our emails
 * These domains are known to have strict email filtering policies
 * that may cause our verification emails to be rejected or blocked
 */
export const PROBLEMATIC_EMAIL_DOMAINS = ["me.com", "icloud.com", "mac.com"];

/**
 * Check if an email address uses a problematic domain
 * @param {string} email - The email address to check
 * @returns {boolean} - True if the domain is problematic, false otherwise
 */
export const isProblematicEmailDomain = (email) => {
  if (!email || typeof email !== "string") {
    return false;
  }

  const domain = email.split("@")[1]?.toLowerCase();
  return PROBLEMATIC_EMAIL_DOMAINS.includes(domain);
};
