/**
 * Shared utilities for plan management across dashboard components
 */

// Plan types
export const PLAN_TYPES = {
  FREE: "free",
  SUBSCRIPTION: "subscription",
  ONE_TIME: "one_time",
  BONUS: "bonus",
  LTD: "ltd",
};

// Plan colors
export const PLAN_COLORS = {
  [PLAN_TYPES.FREE]: {
    badge: "bg-gray-500",
    badgeHover: "hover:bg-gray-600",
    progressBar: "bg-gray-500",
    border: "border-gray-200",
  },
  [PLAN_TYPES.SUBSCRIPTION]: {
    badge: "bg-indigo-500",
    badgeHover: "hover:bg-indigo-600",
    progressBar: "bg-indigo-500",
    border: "border-indigo-500/30",
  },
  [PLAN_TYPES.ONE_TIME]: {
    badge: "bg-green-500",
    badgeHover: "hover:bg-green-600",
    progressBar: "bg-green-600",
    border: "border-green-500/30",
  },
  [PLAN_TYPES.BONUS]: {
    badge: "bg-purple-500",
    badgeHover: "hover:bg-purple-600",
    progressBar: "bg-purple-500",
    border: "border-purple-500/30",
  },
  [PLAN_TYPES.LTD]: {
    badge: "bg-amber-500",
    badgeHover: "hover:bg-amber-600",
    progressBar: "bg-amber-600",
    border: "border-amber-500/30",
  },
};

/**
 * Map source type to plan type
 */
export const mapSourceTypeToPlanType = (source) => {
  if (!source || !source.type) return PLAN_TYPES.FREE;

  switch (source.type) {
    case "free_plan":
      return PLAN_TYPES.FREE;
    case "subscription":
      return PLAN_TYPES.SUBSCRIPTION;
    case "one_time":
      return PLAN_TYPES.ONE_TIME;
    case "bonus":
      return PLAN_TYPES.BONUS;
    case "ltd":
      return PLAN_TYPES.LTD;
    default:
      return PLAN_TYPES.FREE;
  }
};

/**
 * Get plan name based on source information
 */
export const getPlanName = (source) => {
  if (!source || !source.type) return "Unknown Plan";

  if (source.plan) {
    if (source.plan.tier) {
      return source.plan.tier;
    }
    return source.plan.name;
  }

  switch (source.type) {
    case "bonus":
      return "Bonus";
    default:
      return source.type;
  }
};

/**
 * Get plan priority based on source type (lower number = higher priority)
 */
export const getPlanPriority = (source) => {
  if (!source || !source.type) return 6;

  switch (source.type) {
    case "free_plan":
      return 5;
    case "subscription":
      return 3;
    case "one_time":
      return 4;
    case "bonus":
      return 1;
    case "ltd":
      return 2;
    default:
      return 6;
  }
};

/**
 * Get plan type label for display
 */
export const getPlanTypeLabel = (type) => {
  switch (type) {
    case PLAN_TYPES.FREE:
      return "Free";
    case PLAN_TYPES.SUBSCRIPTION:
      return "Subscription";
    case PLAN_TYPES.ONE_TIME:
      return "One-time";
    case PLAN_TYPES.BONUS:
      return "Bonus";
    case PLAN_TYPES.LTD:
      return "AppSumo";
    default:
      return "Free";
  }
};

/**
 * Transform entitlements data to plan objects
 */
export const transformEntitlementsToPlan = (entitlement) => {
  if (!entitlement) return null;

  return {
    id: entitlement.id.toString(),
    name: getPlanName(entitlement.source),
    type: mapSourceTypeToPlanType(entitlement.source),
    totalMinutes: entitlement.totalCredits,
    remainingMinutes: entitlement.remainingCredits,
    resetsAt: entitlement.isRecurring ? entitlement.validUntil : null,
    expiresAt: !entitlement.isRecurring ? entitlement.validUntil : null,
    priority: getPlanPriority(entitlement.source),
    source: entitlement.source,
    isRecurring: entitlement.isRecurring,
    plan: entitlement.source?.plan || null,
  };
};

/**
 * Sort plans by priority (lower number = higher priority)
 */
export const sortPlansByPriority = (plans) => {
  return [...plans].sort((a, b) => a.priority - b.priority);
};

/**
 * Calculate total remaining minutes from entitlements
 */
export const calculateTotalRemainingMinutes = (entitlements) => {
  return (
    entitlements?.reduce(
      (total, entitlement) => total + (entitlement.remainingCredits || 0),
      0
    ) || 0
  );
};

/**
 * Format date for display
 */
export const formatPlanDate = (dateString) => {
  if (!dateString || isNaN(new Date(dateString).getTime())) return null;
  const date = new Date(dateString);
  return date;
};

/**
 * Get action button text based on plan type
 */
export const getActionButtonText = (planType, t) => {
  switch (planType) {
    case PLAN_TYPES.FREE:
      return t("upgradeNow");
    case PLAN_TYPES.SUBSCRIPTION:
      return t("managePlan");
    case PLAN_TYPES.ONE_TIME:
      return t("buyMore");
    case PLAN_TYPES.LTD:
      return t("managePlan");
    default:
      return t("upgradeNow");
  }
};

/**
 * Calculate storage data from API summary
 */
export const calculateStorageData = (summary) => {
  const totalStorageGB = summary?.totalStorage || 0;
  const consumedStorageGB = summary?.consumedStorage || 0;
  const availableStorageGB = parseFloat(
    (totalStorageGB - consumedStorageGB).toFixed(2)
  );

  return {
    totalGB: totalStorageGB,
    usedGB: consumedStorageGB,
    availableGB: availableStorageGB,
  };
};

/**
 * Safe URL opener utility
 */
export const safeOpenUrl = (url) => {
  if (typeof window !== "undefined" && url) {
    window.open(url, "_blank", "noopener,noreferrer");
  }
};

