import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import * as Sentry from "@sentry/nextjs";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export function formatFileSize(size) {
  const units = ["B", "KB", "MB", "GB", "TB"];
  let unitIndex = 0;
  let fileSize = size;

  while (fileSize >= 1024 && unitIndex < units.length - 1) {
    fileSize /= 1024;
    unitIndex++;
  }

  return `${fileSize.toFixed(2)} ${units[unitIndex]}`;
}

export function formatDate(date) {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  return `${year}/${month}/${day}`;
}

// Format date for file list display with internationalization support
// Returns format like "Aug 6, 2025, 9:49 PM" for English
export function formatCreatedTime(dateString, locale = "en") {
  try {
    if (!dateString || isNaN(new Date(dateString).getTime())) {
      return "";
    }

    const date = new Date(dateString);

    // Use Intl.DateTimeFormat for proper internationalization
    return new Intl.DateTimeFormat(locale, {
      year: "numeric",
      month: "short", // Aug, Sep, etc.
      day: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: false, // Use 12-hour format with AM/PM
    }).format(date);
  } catch (error) {
    console.error("Error formatting created time:", error);
    return dateString; // Return original string if formatting fails
  }
}

export function truncateFilename(filename, maxWidth) {
  const canvas = document.createElement("canvas");
  const context = canvas.getContext("2d");
  context.font = "14px sans-serif"; // 根据实际使用的字体和大小调整

  let truncated = filename;
  while (
    context.measureText(truncated).width > maxWidth &&
    truncated.length > 0
  ) {
    truncated = truncated.slice(0, -1);
  }

  return truncated.length < filename.length ? truncated + "..." : filename;
}

// Helper function to check if duration is invalid
export function isInvalidDuration(duration) {
  return duration == null || duration === "" || !isFinite(duration);
}

export function formatDuration(seconds) {
  // Handle null, undefined, empty, or invalid values
  if (isInvalidDuration(seconds)) {
    return "--:--";
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  if (hours > 0) {
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  } else {
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  }
}

// Speaker avatar utilities
const SPEAKER_COLORS = [
  { bg: "bg-blue-500", text: "text-white", border: "border-blue-200" },
  { bg: "bg-green-500", text: "text-white", border: "border-green-200" },
  { bg: "bg-purple-500", text: "text-white", border: "border-purple-200" },
  { bg: "bg-orange-500", text: "text-white", border: "border-orange-200" },
  { bg: "bg-pink-500", text: "text-white", border: "border-pink-200" },
  { bg: "bg-indigo-500", text: "text-white", border: "border-indigo-200" },
  { bg: "bg-red-500", text: "text-white", border: "border-red-200" },
  { bg: "bg-teal-500", text: "text-white", border: "border-teal-200" },
  { bg: "bg-yellow-500", text: "text-black", border: "border-yellow-200" },
  { bg: "bg-cyan-500", text: "text-white", border: "border-cyan-200" },
];

/**
 * 为 speaker 生成一致的颜色主题
 * @param {string} speakerName - speaker 名称 (如 "Speaker 1", "Speaker 2")
 * @returns {object} 包含背景色、文字色、边框色的对象
 */
export function getSpeakerColor(speakerName) {
  // 防护处理：确保 speakerName 是有效字符串
  if (!speakerName || typeof speakerName !== "string") {
    // 如果 speakerName 无效，返回默认颜色（第一个颜色）
    return SPEAKER_COLORS[0];
  }

  const safeSpeakerName = speakerName.trim();
  if (safeSpeakerName === "") {
    // 如果是空字符串，返回默认颜色
    return SPEAKER_COLORS[0];
  }

  // 从 speaker 名称中提取数字，如果没有数字则使用字符串哈希
  const match = safeSpeakerName.match(/\d+/);
  let index;

  if (match) {
    // 如果有数字，使用数字作为索引
    index = (parseInt(match[0]) - 1) % SPEAKER_COLORS.length;
    // 确保索引不为负数
    if (index < 0) {
      index = 0;
    }
  } else {
    // 如果没有数字，使用简单的字符串哈希
    let hash = 0;
    for (let i = 0; i < safeSpeakerName.length; i++) {
      const char = safeSpeakerName.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    index = Math.abs(hash) % SPEAKER_COLORS.length;
  }

  return SPEAKER_COLORS[index];
}

/**
 * 获取 speaker 的首字母用于头像显示
 * @param {string} speakerName - speaker 名称
 * @returns {string} 首字母或数字
 */
export function getSpeakerInitials(speakerName) {
  // 防护处理：确保 speakerName 是有效字符串
  if (!speakerName || typeof speakerName !== "string") {
    return "S"; // 默认值
  }

  const safeSpeakerName = speakerName.trim();
  if (safeSpeakerName === "") {
    return "S"; // 默认值
  }

  // 尝试提取数字
  const match = safeSpeakerName.match(/\d+/);
  if (match) {
    return match[0];
  }

  // 如果没有数字，返回首字母
  const words = safeSpeakerName.split(/\s+/).filter((word) => word.length > 0);
  if (words.length >= 2) {
    return (words[0][0] + words[1][0]).toUpperCase();
  } else if (words.length === 1 && words[0].length > 0) {
    return words[0][0].toUpperCase();
  }

  return "S"; // 默认值
}

// 通用错误信息提取工具
export const getErrorMessage = (error) => {
  if (!error) return "Unknown error";
  if (typeof error === "string") return error;
  if (error.message) return error.message;
  if (error.response && error.response.data && error.response.data.message) {
    return error.response.data.message;
  }
  try {
    return JSON.stringify(error);
  } catch {
    return String(error);
  }
};

export const reportUploadError = ({
  error,
  fileType,
  fileName,
  fileSize,
  userId,
  userEmail,
}) => {
  // 过滤掉 401、403、404 错误，这些是正常的业务场景
  const shouldReport =
    error?.status !== 401 && error?.status !== 403 && error?.status !== 404;

  if (shouldReport) {
    // 创建一个真正的 Error 对象，而不是直接传递可能不是 Error 的对象
    let errorToReport;

    if (error instanceof Error) {
      // 如果已经是 Error 对象，直接使用
      errorToReport = error;
    } else {
      // 如果不是 Error 对象，创建一个新的 Error 对象
      const errorMessage = getErrorMessage(error);
      errorToReport = new Error(errorMessage);

      // 保留原始错误信息
      errorToReport.originalError = error;
    }

    Sentry.captureException(errorToReport, {
      extra: {
        fileType,
        fileName,
        fileSize,
        userId,
        userEmail,
        errorMessage: getErrorMessage(error),
        // 添加原始错误对象的详细信息
        originalErrorData: error?.data,
        originalErrorStatus: error?.status,
        originalErrorHeaders: error?.headers,
        originalErrorConfig: error?.config,
      },
      tags: {
        type: "upload_error",
      },
    });
  }
};
