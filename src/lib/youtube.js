const isValidYouTubeUrl = (url) => {
  // 0. 清理URL中的空白字符（包括换行符、制表符等）
  const cleanUrl = url.trim().replace(/\s/g, "");

  // 1. 基本 URL 格式验证
  try {
    // 如果URL没有协议，但看起来像YouTube域名，则添加https://前缀进行验证
    let urlToValidate = cleanUrl;
    if (!cleanUrl.startsWith("http://") && !cleanUrl.startsWith("https://")) {
      // 检查是否包含YouTube相关域名
      if (cleanUrl.includes("youtube.com") || cleanUrl.includes("youtu.be")) {
        urlToValidate = `https://${cleanUrl}`;
      }
    }
    new URL(urlToValidate);
  } catch (e) {
    return {
      isValid: false,
      reason: "Invalid URL format",
    };
  }

  // 2. 检查是否包含嵌套的 YouTube URL
  const extractNestedYouTubeUrl = (inputUrl) => {
    // 查找嵌套的 YouTube URL 模式
    const nestedPatterns = [
      // 匹配 youtube.com/watch?v= 格式
      {
        pattern:
          /https?:\/\/(?:www\.|m\.)?youtube\.com\/watch\?(?=.*v=([^&]{11,}))(?:\S+)?/g,
        idIndex: 1,
      },
      // 匹配 youtu.be/ 格式
      {
        pattern: /https?:\/\/(?:www\.)?youtu\.be\/([^?]{11,})(?:\?.*)?/g,
        idIndex: 1,
      },
      // 匹配 youtube.com/embed/ 格式
      {
        pattern:
          /https?:\/\/(?:www\.|m\.)?youtube\.com\/embed\/([^?]{11,})(?:\?.*)?/g,
        idIndex: 1,
      },
      // 匹配 youtube.com/shorts/ 格式
      {
        pattern:
          /https?:\/\/(?:www\.|m\.)?youtube\.com\/shorts\/([^?]{11,})(?:\?.*)?/g,
        idIndex: 1,
      },
      // 匹配 youtube.com/live/ 格式
      {
        pattern:
          /https?:\/\/(?:www\.|m\.)?youtube\.com\/live\/([^?]{11,})(?:\?.*)?/g,
        idIndex: 1,
      },
    ];

    for (const { pattern, idIndex } of nestedPatterns) {
      const matches = [...inputUrl.matchAll(pattern)];
      if (matches.length > 0) {
        // 如果找到多个匹配，使用第一个
        const videoId = matches[0][idIndex];
        if (videoId && /^[A-Za-z0-9_-]{11,}$/.test(videoId)) {
          // 构造标准化的 YouTube URL
          return `https://www.youtube.com/watch?v=${videoId}`;
        }
      }
    }
    return null;
  };

  // 尝试提取嵌套的 YouTube URL
  const nestedUrl = extractNestedYouTubeUrl(cleanUrl);
  const urlToCheck = nestedUrl || cleanUrl;

  // 3. YouTube URL 格式和 Video ID 验证
  const patterns = [
    /^(?:https?:\/\/)?(?:www\.|m\.)?youtube\.com\/watch\?(?=.*v=([^&]{11,}))(?:\S+)?$/,
    /^(?:https?:\/\/)?(?:www\.)?youtu\.be\/([^?]{11,})(?:\?.*)?$/,
    /^(?:https?:\/\/)?(?:www\.|m\.)?youtube\.com\/embed\/([^?]{11,})(?:\?.*)?$/,
    /^(?:https?:\/\/)?(?:www\.|m\.)?youtube\.com\/shorts\/([^?]{11,})(?:\?.*)?$/,
    /^(?:https?:\/\/)?(?:www\.|m\.)?youtube\.com\/live\/([^?]{11,})(?:\?.*)?$/,
  ];

  for (const pattern of patterns) {
    const match = urlToCheck.match(pattern);
    if (match) {
      const videoId = match[1];
      // 验证 video ID 是否符合 YouTube 规则（至少11位字母数字和特定字符）
      if (/^[A-Za-z0-9_-]{11,}$/.test(videoId)) {
        return {
          isValid: true,
          videoId: videoId,
          originalUrl: url,
          extractedUrl: nestedUrl || cleanUrl,
        };
      }
    }
  }

  return {
    isValid: false,
    reason: "Invalid YouTube video URL",
  };
};

export default isValidYouTubeUrl;
