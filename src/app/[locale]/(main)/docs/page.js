import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter";
import { tomorrow } from "react-syntax-highlighter/dist/cjs/styles/prism";
import { generateMultilingualId } from "@/lib/slug-utils";
import fs from "fs/promises";
import path from "path";
import dynamic from "next/dynamic";
import { generateConsistentMetadata } from "@/lib/metadata";

// 动态导入 TableOfContents 组件
const TableOfContents = dynamic(
  () => import("@/components/Blog/TableOfContents"),
  {
    ssr: false,
  }
);

// 生成元数据
export async function generateMetadata({ params: { locale } }) {
  return generateConsistentMetadata("/docs", locale, {
    title: "UniScribe OpenAPI Documentation",
    description:
      "Complete API documentation for integrating UniScribe transcription services into your applications and workflows.",
  });
}

// 从 markdown 文件中提取标题
function extractHeadings(content) {
  const headings = [];
  const lines = content.split("\n");

  for (const line of lines) {
    const match = line.match(/^(#{1,6})\s+(.+)$/);
    if (match) {
      const level = match[1].length;
      const text = match[2].trim();
      const id = generateMultilingualId(text);

      headings.push({
        level,
        text,
        id,
      });
    }
  }

  return headings;
}

// 读取 OpenAPI markdown 文件
async function getOpenApiContent() {
  try {
    const filePath = path.join(process.cwd(), "contents/docs", "OpenAPI.md");
    const content = await fs.readFile(filePath, "utf-8");
    const headings = extractHeadings(content);

    return {
      content,
      headings,
    };
  } catch (error) {
    console.error("Error reading OpenAPI.md:", error);
    return {
      content: "# OpenAPI Documentation\n\nDocumentation not found.",
      headings: [],
    };
  }
}

export default async function ApiDocsPage({ params }) {
  const { content, headings } = await getOpenApiContent();

  return (
    <article className="container py-2 mt-20 flex">
      <div className="flex-1">
        <div className="flex flex-row">
          <div className="prose max-w-none flex-1">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                // 代码块处理
                code: ({ node, inline, className, children, ...props }) => {
                  const match = /language-(\w+)/.exec(className || "");
                  const language = match ? match[1] : "";

                  if (!inline && language) {
                    return (
                      <SyntaxHighlighter
                        style={tomorrow}
                        language={language}
                        PreTag="div"
                        className="rounded-md my-4"
                        {...props}
                      >
                        {String(children).replace(/\n$/, "")}
                      </SyntaxHighlighter>
                    );
                  }

                  // 内联代码
                  return (
                    <code
                      className="bg-gray-100 text-gray-800 px-1.5 py-0.5 rounded text-sm font-mono"
                      {...props}
                    >
                      {children}
                    </code>
                  );
                },

                // 无序列表
                ul: ({ children }) => (
                  <ul className="list-disc list-outside ml-6 my-4 space-y-2">
                    {children}
                  </ul>
                ),

                // 有序列表
                ol: ({ children }) => (
                  <ol className="list-decimal list-outside ml-6 my-4 space-y-2">
                    {children}
                  </ol>
                ),

                // 列表项
                li: ({ children }) => (
                  <li className="text-gray-600 leading-relaxed">{children}</li>
                ),

                // 加粗文本
                strong: ({ children }) => (
                  <strong className="text-base font-semibold text-gray-800">
                    {children}
                  </strong>
                ),

                // 链接处理
                a: ({ href, children }) => {
                  const fullHref = href?.startsWith("http")
                    ? href
                    : `https://${href}`;

                  return (
                    <a
                      href={fullHref}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-custom-500 hover:text-custom-600 underline"
                    >
                      {children}
                    </a>
                  );
                },

                // 链接处理
                a: ({ href, children, ...props }) => {
                  // 如果是锚点链接（以 # 开头），保持原样
                  if (href && href.startsWith("#")) {
                    return (
                      <a
                        href={href}
                        className="text-custom-500 hover:text-custom-600 underline"
                        {...props}
                      >
                        {children}
                      </a>
                    );
                  }
                  // 外部链接
                  return (
                    <a
                      href={href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-custom-500 hover:text-custom-600 underline"
                      {...props}
                    >
                      {children}
                    </a>
                  );
                },

                // 段落处理
                p: ({ children, ...props }) => {
                  const isInsideListItem =
                    props?.node?.parent?.type === "listItem";
                  if (isInsideListItem) {
                    return <>{children}</>;
                  }
                  return (
                    <p className="text-gray-600 my-4 text-[16px] leading-relaxed">
                      {children}
                    </p>
                  );
                },

                // 标题处理
                h1: ({ children }) => {
                  const text = children.toString();
                  const id = generateMultilingualId(text);
                  return (
                    <h1
                      id={id}
                      className="text-3xl font-bold mb-6 mt-8 pb-2 relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-full after:bg-custom-500"
                    >
                      {text}
                    </h1>
                  );
                },

                h2: ({ children }) => {
                  const text = children.toString();
                  const id = generateMultilingualId(text);
                  return (
                    <h2
                      id={id}
                      className="text-2xl font-semibold mb-6 mt-10 pb-2 relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[1px] after:w-full after:bg-gray-200"
                    >
                      {text}
                    </h2>
                  );
                },

                h3: ({ children }) => {
                  const text = children.toString();
                  const id = generateMultilingualId(text);
                  return (
                    <h3 id={id} className="text-xl font-semibold mb-4 mt-6">
                      {children}
                    </h3>
                  );
                },

                h4: ({ children }) => {
                  const text = children.toString();
                  const id = generateMultilingualId(text);
                  return (
                    <h4 id={id} className="text-lg font-semibold mb-4 mt-4">
                      {children}
                    </h4>
                  );
                },

                // 表格处理
                table: ({ children }) => (
                  <div className="overflow-x-auto my-6">
                    <table className="min-w-full border-collapse border border-gray-300">
                      {children}
                    </table>
                  </div>
                ),

                thead: ({ children }) => (
                  <thead className="bg-gray-50">{children}</thead>
                ),

                th: ({ children }) => (
                  <th className="border border-gray-300 px-4 py-2 text-left font-semibold text-gray-800">
                    {children}
                  </th>
                ),

                td: ({ children }) => (
                  <td className="border border-gray-300 px-4 py-2 text-gray-600">
                    {children}
                  </td>
                ),

                // 引用块
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-custom-500 pl-4 my-4 italic text-gray-700">
                    {children}
                  </blockquote>
                ),
              }}
            >
              {content}
            </ReactMarkdown>
          </div>

          {/* 侧边栏目录 */}
          <aside className="sticky top-24 h-fit ml-8 hidden md:block space-y-4">
            <TableOfContents headings={headings} />
          </aside>
        </div>
      </div>
    </article>
  );
}
