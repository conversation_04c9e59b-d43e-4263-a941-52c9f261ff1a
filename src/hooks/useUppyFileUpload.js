import { useState, useRef, useEffect } from "react";
import { uploadService } from "@/services/api/uploadService";
import { calculateFileChecksum } from "@/lib/fileUtils";
import { createFileProcessor } from "@/lib/mediaProcessing";
import { useFFmpegProcessor } from "@/hooks/useFFmpegProcessor";
import { storageService } from "@/services/storageService";
import { useTranslations } from "next-intl";
import { externalInstance } from "@/services/api/axiosInstance";
import { UPLOAD_CONFIG } from "@/config/upload";
import { toast } from "sonner";

// Uppy 相关导入
import Uppy from "@uppy/core";
import XHRUpload from "@uppy/xhr-upload";
import AwsS3 from "@uppy/aws-s3";

/**
 * 判断是否应该使用分块上传
 * @param {File} file - 文件对象
 * @returns {boolean} - 是否使用分块上传
 */
const shouldUseMultipartUpload = (file) => {
  return file.size > UPLOAD_CONFIG.multipart.threshold;
};

/**
 * 使用 Uppy 作为底层引擎的文件上传 Hook
 *
 * 🎯 目标：完全兼容现有的 useFileUpload API，但使用 Uppy 作为底层引擎
 *
 * ✅ 保持现有 UI 完全不变
 * ✅ 获得 Uppy 的稳定性和重试机制
 * ✅ 支持断点续传（后续可启用）
 * ✅ 更好的错误处理和网络状态管理
 * ✅ 支持大文件分块上传
 */
export const useUppyFileUpload = () => {
  const t = useTranslations("common.fileUploadStatus");

  // 1. State management - 与原有 hook 完全相同的 API
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadState, setUploadState] = useState("idle");
  const [fileDuration, setFileDuration] = useState(null);
  const [transcriptionFileId, setTranscriptionFileId] = useState(null);
  const [showAlert, setShowAlert] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // 新增：网速和剩余时间状态（仅用于 multipart 上传）
  const [uploadSpeed, setUploadSpeed] = useState(0); // bytes per second
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState(0); // seconds
  const [isMultipartUpload, setIsMultipartUpload] = useState(false);

  // 2. Refs
  const abortControllerRef = useRef(null);
  const uppyInstanceRef = useRef(null);

  // 用于网速计算的 refs
  const uploadStartTimeRef = useRef(null);
  const lastProgressTimeRef = useRef(null);
  const lastBytesUploadedRef = useRef(0);

  // 3. External hooks
  const {
    processFile,
    interruptProcessing,
    processing,
    progress: processingProgress,
  } = useFFmpegProcessor();

  // 4. 清理 Uppy 插件的公共函数
  const cleanupUppyPlugins = (uppy) => {
    // 清除之前的文件
    uppy.getFiles().forEach((f) => uppy.removeFile(f.id));

    // 移除所有可能的插件（确保干净的状态）
    if (uppy.getPlugin("aws-s3")) {
      if (UPLOAD_CONFIG.debug) {
        console.log("🧹 Uppy: Removing existing aws-s3 plugin");
      }
      uppy.removePlugin(uppy.getPlugin("aws-s3"));
    }
    if (uppy.getPlugin("xhr-upload")) {
      if (UPLOAD_CONFIG.debug) {
        console.log("🧹 Uppy: Removing existing xhr-upload plugin");
      }
      uppy.removePlugin(uppy.getPlugin("xhr-upload"));
    }
  };

  // 5. 初始化 Uppy 实例（轻量级配置）
  useEffect(() => {
    if (typeof window === "undefined") return;

    const uppy = new Uppy({
      id: "uniscribe-file-upload",
      autoProceed: false,
      allowMultipleUploads: false,
      debug: UPLOAD_CONFIG.debug, // 根据环境配置调试模式
      restrictions: {
        maxFileSize: UPLOAD_CONFIG.maxFileSize,
        minFileSize: UPLOAD_CONFIG.minFileSize,
        maxNumberOfFiles: 1, // TODO: 后续支持批量上传时修改
        allowedFileTypes: UPLOAD_CONFIG.allowedFileTypes,
      },
    });

    // 监听上传进度
    uppy.on("upload-progress", (file, progress) => {
      let percentage = 0;

      // Uppy 标准格式：progress 对象包含 bytesUploaded 和 bytesTotal
      if (
        progress.bytesUploaded !== undefined &&
        progress.bytesTotal !== undefined &&
        progress.bytesTotal > 0
      ) {
        percentage = Math.round(
          (progress.bytesUploaded / progress.bytesTotal) * 100
        );

        // 计算网速和剩余时间（仅用于 multipart 上传）
        const currentTime = Date.now();
        const bytesUploaded = progress.bytesUploaded;
        const bytesTotal = progress.bytesTotal;

        // 初始化时间记录
        if (!uploadStartTimeRef.current) {
          uploadStartTimeRef.current = currentTime;
          lastProgressTimeRef.current = currentTime;
          lastBytesUploadedRef.current = bytesUploaded;
        }

        // 计算网速（每 5 秒更新一次，提高准确性）
        const timeSinceLastUpdate = currentTime - lastProgressTimeRef.current;
        if (timeSinceLastUpdate >= 5000) {
          const bytesDiff = bytesUploaded - lastBytesUploadedRef.current;
          const timeDiffSeconds = timeSinceLastUpdate / 1000;
          const currentSpeed = bytesDiff / timeDiffSeconds; // bytes per second

          // 只有当速度合理时才更新（避免异常值）
          if (currentSpeed > 0 && currentSpeed < 1024 * 1024 * 1024) {
            // 最大1GB/s
            // 使用移动平均来平滑网速显示
            setUploadSpeed((prevSpeed) => {
              const smoothingFactor = 0.4; // 稍微增加平滑系数
              const newSpeed =
                prevSpeed === 0
                  ? currentSpeed
                  : prevSpeed * (1 - smoothingFactor) +
                    currentSpeed * smoothingFactor;

              if (UPLOAD_CONFIG.debug) {
                console.log("📊 Uppy: Speed calculation", {
                  currentSpeed:
                    (currentSpeed / (1024 * 1024)).toFixed(2) + " MB/s",
                  smoothedSpeed:
                    (newSpeed / (1024 * 1024)).toFixed(2) + " MB/s",
                  bytesUploaded,
                  bytesTotal,
                  percentage:
                    Math.round((bytesUploaded / bytesTotal) * 100) + "%",
                });
              }

              return newSpeed;
            });

            // 计算剩余时间（使用当前速度）
            const remainingBytes = bytesTotal - bytesUploaded;
            if (currentSpeed > 0) {
              const estimatedSeconds = Math.ceil(remainingBytes / currentSpeed);
              // 限制最大显示时间为24小时，避免显示过大的数值
              const finalEstimate = Math.min(estimatedSeconds, 24 * 3600);
              setEstimatedTimeRemaining(finalEstimate);

              if (UPLOAD_CONFIG.debug) {
                console.log("⏱️ Uppy: Time estimation", {
                  remainingBytes,
                  currentSpeed:
                    (currentSpeed / (1024 * 1024)).toFixed(2) + " MB/s",
                  estimatedSeconds,
                  finalEstimate: finalEstimate + "s",
                });
              }
            }
          }

          // 更新记录
          lastProgressTimeRef.current = currentTime;
          lastBytesUploadedRef.current = bytesUploaded;
        }
      }

      setUploadProgress(percentage);
    });

    // 监听上传成功 - 只设置为 completing，等待后续 completeUpload 调用
    uppy.on("upload-success", (file, response) => {
      setUploadState("completing");
    });

    // 监听上传错误
    uppy.on("upload-error", (file, error) => {
      setUploadState("error");
      setErrorMessage(error.message || "Upload failed");
    });

    // 监听网络连接状态
    uppy.on("back-online", () => {
      toast.success(t("networkOnline"), {
        description: t("networkOnlineDescription"),
        duration: 3000,
      });
    });

    uppy.on("is-offline", () => {
      toast.warning(t("networkOffline"), {
        description: t("networkOfflineDescription"),
        duration: 5000,
      });
    });

    uppyInstanceRef.current = uppy;

    return () => {
      if (uppy) {
        uppy.destroy();
      }
    };
  }, []);

  // 5. 尝试从处理后的文件重新读取时长
  const tryReadDurationFromProcessedFile = async (
    originalDuration,
    originalFile,
    processedFile,
    fileToUpload
  ) => {
    // 如果原始 duration 不为 null 或文件未经过处理，直接返回原始 duration
    if (originalDuration !== null || processedFile === originalFile) {
      return originalDuration;
    }

    if (UPLOAD_CONFIG.debug) {
      console.log(
        "🎵 Uppy: Original duration was null, attempting to read duration from processed file..."
      );
    }

    try {
      const { calculateFileDuration } = await import("@/lib/fileUtils");
      const processedDuration = await calculateFileDuration(fileToUpload);

      if (processedDuration !== null) {
        if (UPLOAD_CONFIG.debug) {
          console.log(
            `🎵 Uppy: Successfully read duration from processed file: ${processedDuration}s`
          );
        }
        return processedDuration;
      } else {
        if (UPLOAD_CONFIG.debug) {
          console.log(
            "🎵 Uppy: Could not read duration from processed file, will use backend preprocessing"
          );
        }
        return null;
      }
    } catch (error) {
      if (UPLOAD_CONFIG.debug) {
        console.warn(
          "🎵 Uppy: Error reading duration from processed file:",
          error.message
        );
      }
      // 返回 null，让后端处理
      return null;
    }
  };

  // 6. 文件处理函数 - 与原有实现完全相同
  const handleFileProcessing = async (file) => {
    try {
      const processorConfig = createFileProcessor(file);
      if (!processorConfig) return file; // No processing needed

      const processedFile = await processFile(file, processorConfig);
      if (!processedFile || processedFile.size === 0) {
        throw new Error("File processing resulted in empty output");
      }

      return processedFile;
    } catch (error) {
      if (error.name === "AbortError") {
        throw error;
      }
      setUploadState("idle");
      throw error; // Let the component handle error tracking
    }
  };

  // 6a. 分块上传到 Cloudflare - 使用 AwsS3 插件
  const uploadToCloudflareMultipart = async (
    file,
    md5HashBase64,
    duration,
    forceUpload,
    currentLanguageCode,
    currentEnableSubtitle,
    folderId
  ) => {
    const uppy = uppyInstanceRef.current;
    if (!uppy) {
      throw new Error("Uppy instance not initialized");
    }

    return new Promise((resolve, reject) => {
      // 用于存储 transcriptionFileId
      let multipartTranscriptionFileId = null;

      // 清除之前的文件和插件
      cleanupUppyPlugins(uppy);

      try {
        if (UPLOAD_CONFIG.debug) {
          console.log("📁 Uppy: Starting multipart upload...", {
            name: file.name,
            type: file.type,
            size: file.size,
            threshold: UPLOAD_CONFIG.multipart.threshold,
          });
        }

        const fileId = uppy.addFile({
          name: file.name,
          type: file.type,
          data: file,
        });

        // 配置 AwsS3 插件用于分块上传
        if (UPLOAD_CONFIG.debug) {
          console.log("🔧 Uppy: Configuring AwsS3 plugin with:", {
            chunkSize: UPLOAD_CONFIG.multipart.chunkSize,
            chunkSizeMB: UPLOAD_CONFIG.multipart.chunkSize / (1024 * 1024),
            limit: UPLOAD_CONFIG.multipart.limit,
            retryDelays: UPLOAD_CONFIG.multipart.retryDelays,
          });
        }

        uppy.use(AwsS3, {
          id: "aws-s3",
          shouldUseMultipart: true,

          createMultipartUpload: async (file) => {
            if (UPLOAD_CONFIG.debug) {
              console.log("🔄 Uppy: Creating multipart upload...");
            }

            const response = await uploadService.createMultipartUpload(
              file.data,
              md5HashBase64,
              duration,
              forceUpload,
              currentLanguageCode,
              currentEnableSubtitle,
              folderId
            );

            if (UPLOAD_CONFIG.debug) {
              console.log("✅ Uppy: Multipart upload created", response.data);
            }

            // 保存 transcriptionFileId
            multipartTranscriptionFileId = response.data.transcriptionFileId;
            setTranscriptionFileId(response.data.transcriptionFileId);

            return {
              uploadId: response.data.uploadId,
              key: response.data.key,
            };
          },

          listParts: async (file, { uploadId }) => {
            if (UPLOAD_CONFIG.debug) {
              console.log("🔄 Uppy: Listing parts for uploadId:", uploadId);
            }

            const response = await uploadService.listMultipartParts(uploadId);
            return response.data.parts || [];
          },

          signPart: async (file, { uploadId, partNumber }) => {
            if (UPLOAD_CONFIG.debug) {
              console.log(
                `🔄 Uppy: Signing part ${partNumber} for uploadId:`,
                uploadId
              );
            }

            const response = await uploadService.signMultipartPart(
              uploadId,
              partNumber
            );
            return { url: response.data.signedUrl };
          },

          completeMultipartUpload: async (file, { uploadId, parts }) => {
            if (UPLOAD_CONFIG.debug) {
              console.log("🔄 Uppy: Completing multipart upload...", {
                uploadId,
                partsCount: parts.length,
                rawParts: parts,
              });
            }

            // 🔥 修复：清理 parts 数据，只保留 AWS S3 API 需要的字段
            const cleanedParts = parts.map((part) => ({
              ETag: part.ETag || part.etag, // 使用大写的 ETag
              PartNumber: part.PartNumber,
              // 移除不支持的字段如 content-length, etag (小写) 等
            }));

            if (UPLOAD_CONFIG.debug) {
              console.log("🔄 Uppy: Cleaned parts data:", cleanedParts);
            }

            const response = await uploadService.completeMultipartUpload(
              uploadId,
              cleanedParts
            );
            return response.data;
          },

          abortMultipartUpload: async (file, { uploadId }) => {
            if (UPLOAD_CONFIG.debug) {
              console.log("🔄 Uppy: Aborting multipart upload:", uploadId);
            }

            await uploadService.abortMultipartUpload(uploadId);
          },

          // 分块配置 - 使用正确的 Uppy API
          getChunkSize: (file) => {
            if (UPLOAD_CONFIG.debug) {
              console.log("🔧 Uppy: getChunkSize called for file:", {
                fileName: file.name,
                fileSize: file.size,
                configuredChunkSize: UPLOAD_CONFIG.multipart.chunkSize,
              });
            }
            return UPLOAD_CONFIG.multipart.chunkSize;
          },
          retryDelays: UPLOAD_CONFIG.multipart.retryDelays,
          limit: UPLOAD_CONFIG.multipart.limit,
        });

        // 设置事件监听器
        const handleUploadSuccess = () => {
          if (UPLOAD_CONFIG.debug) {
            console.log("✅ Uppy: Multipart upload completed successfully", {
              transcriptionFileId: multipartTranscriptionFileId,
            });
          }
          resolve(multipartTranscriptionFileId);
        };

        const handleUploadError = (file, error) => {
          if (UPLOAD_CONFIG.debug) {
            console.error("❌ Uppy: Multipart upload failed", error);
          }
          reject(error);
        };

        uppy.once("upload-success", handleUploadSuccess);
        uppy.once("upload-error", handleUploadError);

        // 开始上传
        uppy.upload().catch(reject);
      } catch (error) {
        if (UPLOAD_CONFIG.debug) {
          console.error("❌ Uppy: Error setting up multipart upload", error);
        }
        reject(error);
      }
    });
  };

  // 6b. 单次上传到 Cloudflare - 使用 XHR 插件（保持现有逻辑）
  const uploadToCloudflare = async (preSignedUrl, file) => {
    const uppy = uppyInstanceRef.current;
    if (!uppy) {
      throw new Error("Uppy instance not initialized");
    }

    return new Promise((resolve, reject) => {
      // 清除之前的文件和插件
      cleanupUppyPlugins(uppy);

      // 添加文件到 Uppy
      try {
        if (UPLOAD_CONFIG.debug) {
          console.log("📁 Uppy: Adding file to Uppy instance...", {
            name: file.name,
            type: file.type,
            size: file.size,
          });
        }

        const fileId = uppy.addFile({
          name: file.name,
          type: file.type,
          data: file,
        });

        if (UPLOAD_CONFIG.debug) {
          console.log("✅ Uppy: File added successfully, ID:", fileId);
        }

        // 配置 XHR 上传插件
        uppy.use(XHRUpload, {
          id: "xhr-upload",
          endpoint: preSignedUrl,
          method: "PUT",
          formData: false, // 关键修复：不使用 FormData，直接发送文件内容
          headers: {
            "Content-Type": file.type,
          },
          limit: 1,
          timeout: 30 * 60 * 1000, // 30分钟超时
          // 自定义重试逻辑（可选，默认已有重试机制）
          shouldRetry(xhr) {
            console.log(
              "🔄 Uppy: Checking if should retry, status:",
              xhr.status
            );
            // 默认重试网络错误、5xx 错误等
            return xhr.status === 0 || (xhr.status >= 500 && xhr.status < 600);
          },
          // 处理非 JSON 响应
          getResponseData(xhr) {
            if (UPLOAD_CONFIG.debug) {
              console.log("📤 Uppy: getResponseData called with xhr:", {
                status: xhr.status,
                statusText: xhr.statusText,
                responseType: xhr.responseType,
                responseText: xhr.responseText?.substring(0, 100) + "...",
                response: typeof xhr.response,
              });
            }

            // 检查上传是否成功
            if (xhr.status >= 200 && xhr.status < 300) {
              if (UPLOAD_CONFIG.debug) {
                console.log("✅ Uppy: Upload successful, status:", xhr.status);
              }
              return {
                url: preSignedUrl, // 使用预签名 URL 作为文件 URL
                status: xhr.status,
                statusText: xhr.statusText,
              };
            } else {
              // 错误响应
              console.error("❌ Uppy: Upload failed, status:", xhr.status);
              throw new Error(
                `Upload failed with status ${xhr.status}: ${xhr.statusText}`
              );
            }
          },
        });

        // 监听重试事件
        const handleRetry = (file) => {
          if (file.id === fileId) {
            console.log("🔄 Uppy: Retrying upload for file:", file.name);
          }
        };

        // 监听上传停滞事件
        const handleStalled = (error, files) => {
          const stalledFile = files.find((f) => f.id === fileId);
          if (stalledFile) {
            console.log(
              "⏳ Uppy: Upload stalled for file:",
              stalledFile.name,
              error
            );
          }
        };

        // 监听这次上传的结果
        const handleSuccess = (uploadedFile, response) => {
          if (uploadedFile.id === fileId) {
            console.log("🎉 Uppy: Upload completed successfully!");
            uppy.off("upload-success", handleSuccess);
            uppy.off("upload-error", handleError);
            uppy.off("upload-retry", handleRetry);
            uppy.off("upload-stalled", handleStalled);
            uppy.off("upload-progress", handleProgress);
            resolve(response);
          }
        };

        const handleError = (uploadedFile, error) => {
          if (uploadedFile.id === fileId) {
            console.error("❌ Uppy: Upload failed permanently:", error);
            uppy.off("upload-success", handleSuccess);
            uppy.off("upload-error", handleError);
            uppy.off("upload-retry", handleRetry);
            uppy.off("upload-stalled", handleStalled);
            uppy.off("upload-progress", handleProgress);
            reject(error);
          }
        };

        // 添加更多调试事件监听
        const handleProgress = (file, progress) => {
          if (file.id === fileId) {
            let percentage = 0;

            // Uppy 标准格式：progress 对象包含 bytesUploaded 和 bytesTotal
            if (
              progress.bytesUploaded !== undefined &&
              progress.bytesTotal !== undefined &&
              progress.bytesTotal > 0
            ) {
              percentage = Math.round(
                (progress.bytesUploaded / progress.bytesTotal) * 100
              );
            }

            console.log(
              `📊 Uppy: Upload progress for ${file.name}: ${percentage}%`,
              {
                bytesUploaded: progress.bytesUploaded,
                bytesTotal: progress.bytesTotal,
              }
            );
          }
        };

        uppy.on("upload-success", handleSuccess);
        uppy.on("upload-error", handleError);
        uppy.on("upload-retry", handleRetry);
        uppy.on("upload-stalled", handleStalled);
        uppy.on("upload-progress", handleProgress);

        // 开始上传
        uppy.upload().catch(reject);
      } catch (error) {
        reject(error);
      }
    });
  };

  // 7. 主上传函数 - 保持与原有 API 完全相同
  const uploadFile = async (file, duration, folderId = null) => {
    try {
      if (UPLOAD_CONFIG.debug) {
        console.log("🚀 Uppy: Starting upload process...", {
          fileName: file.name,
          fileSize: file.size,
          duration,
          folderId,
        });
      }

      setUploadState("processing");
      setUploadProgress(0);

      // 🔄 步骤1：先进行 FFmpeg 处理（与原有逻辑保持一致）
      if (UPLOAD_CONFIG.debug) {
        console.log("🔄 Uppy: Starting file processing...", {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
        });
      }

      const processedFile = await handleFileProcessing(file);

      // 确保有有效的文件用于上传
      const fileToUpload =
        processedFile && processedFile.size !== undefined
          ? processedFile
          : file;

      if (UPLOAD_CONFIG.debug) {
        console.log("📁 Uppy: File processing completed", {
          originalSize: file.size,
          processedSize: fileToUpload.size,
          wasProcessed: processedFile !== file,
        });
      }

      // 🎵 尝试从处理后的文件重新读取时长
      const finalDuration = await tryReadDurationFromProcessedFile(
        duration,
        file,
        processedFile,
        fileToUpload
      );

      // 🔄 步骤2：基于处理后的文件决定上传策略
      const useMultipart = shouldUseMultipartUpload(fileToUpload);

      // 设置 multipart 标志，用于 UI 显示网速和剩余时间
      setIsMultipartUpload(useMultipart);

      if (UPLOAD_CONFIG.debug) {
        console.log("📤 Uppy: Upload strategy determined", {
          useMultipart,
          originalFileSize: file.size,
          processedFileSize: fileToUpload.size,
          threshold: UPLOAD_CONFIG.multipart.threshold,
        });
      }

      setUploadState("uploading");

      if (UPLOAD_CONFIG.debug) {
        console.log("🔐 Uppy: Calculating file checksum...");
      }
      const md5HashBase64 = await calculateFileChecksum(fileToUpload);
      const forceUpload = true;

      if (UPLOAD_CONFIG.debug) {
        console.log(
          "🔐 Uppy: File checksum calculated:",
          md5HashBase64.substring(0, 10) + "..."
        );
      }

      // 读取语言设置
      let currentLanguageCode = "en";
      if (typeof window !== "undefined" && window.localStorage) {
        const storedLang = localStorage.getItem("audio_language_preference");
        if (storedLang) {
          currentLanguageCode = storedLang;
        }
      }

      // 读取字幕设置
      let currentEnableSubtitle = false;
      if (typeof window !== "undefined") {
        const storedPreference = storageService.getSubtitlePreference();
        if (storedPreference !== null) {
          currentEnableSubtitle = storedPreference;
        }
      }

      // 声明变量用于存储 transcriptionFileId
      let currentTranscriptionFileId = null;

      if (useMultipart) {
        // 使用分块上传 - 直接调用分块上传函数
        currentTranscriptionFileId = await uploadToCloudflareMultipart(
          fileToUpload,
          md5HashBase64,
          finalDuration,
          forceUpload,
          currentLanguageCode,
          currentEnableSubtitle,
          folderId
        );
      } else {
        // 使用单次上传 - 先获取预签名 URL
        if (UPLOAD_CONFIG.debug) {
          console.log("📡 Uppy: Requesting signed URL from backend...");
        }

        const response = await uploadService.generateSignedUrl(
          fileToUpload,
          md5HashBase64,
          finalDuration,
          forceUpload,
          currentLanguageCode,
          currentEnableSubtitle,
          folderId
        );

        if (UPLOAD_CONFIG.debug) {
          console.log("✅ Uppy: Signed URL received successfully", {
            transcriptionFileId: response.data.transcriptionFileId,
            hasPreSignedUrl: !!response.data.preSignedUrl,
          });
        }

        const {
          transcriptionFileId: responseTranscriptionFileId,
          preSignedUrl,
        } = response.data;
        currentTranscriptionFileId = responseTranscriptionFileId;
        setTranscriptionFileId(responseTranscriptionFileId);

        // 执行单次上传
        await uploadToCloudflare(preSignedUrl, fileToUpload);
      }

      if (UPLOAD_CONFIG.debug) {
        console.log("✅ Uppy: Upload to Cloudflare completed", {
          useMultipart,
          transcriptionFileId: currentTranscriptionFileId,
        });
      }

      // 完成上传（只有单次上传需要调用 completeUpload）
      if (!useMultipart) {
        const completeResponse = await uploadService.completeUpload(
          currentTranscriptionFileId
        );

        if (UPLOAD_CONFIG.debug) {
          console.log("✅ Uppy: Upload completion confirmed by backend");
        }

        if (completeResponse.status !== 200) {
          const completeError = new Error("Upload completion failed");
          completeError.response = completeResponse;
          throw completeError;
        }
      }

      // 上传成功 - 重置网速相关状态
      setUploadState("success");
      setIsMultipartUpload(false);
      setUploadSpeed(0);
      setEstimatedTimeRemaining(0);
      uploadStartTimeRef.current = null;
      lastProgressTimeRef.current = null;
      lastBytesUploadedRef.current = 0;

      return currentTranscriptionFileId;
    } catch (error) {
      console.error("File upload failed:", error);

      if (error.name === "AbortError" || error.message === "upload canceled") {
        console.log("Upload was canceled by user");
        return;
      } else {
        let errorMsg = "An unexpected error occurred during upload";

        if (error.response?.status === 403) {
          errorMsg = error.response?.data?.message || t("error.invalidFolder");
        } else if (error.response?.data?.message) {
          errorMsg = error.response.data.message;
        } else if (error.data?.message) {
          errorMsg = error.data.message;
        } else if (error.message) {
          errorMsg = error.message;
        }

        setErrorMessage(`${errorMsg}`);
        setShowAlert(true);
      }

      // 重置网速相关状态
      setIsMultipartUpload(false);
      setUploadSpeed(0);
      setEstimatedTimeRemaining(0);
      uploadStartTimeRef.current = null;
      lastProgressTimeRef.current = null;
      lastBytesUploadedRef.current = 0;

      setUploadState("idle");
      throw error;
    }
  };

  // 8. 取消上传功能
  const cancelUpload = () => {
    // 取消 Uppy 上传
    if (uppyInstanceRef.current) {
      uppyInstanceRef.current.cancelAll();
    }

    // 取消 axios 请求（如果有）
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    // 停止处理（如果正在进行）
    if (processing) {
      interruptProcessing();
    }

    resetUploadStates();
  };

  // 9. 重置状态
  const resetUploadStates = () => {
    setSelectedFile(null);
    setFileDuration(null);
    setUploadProgress(0);
    setUploadState("idle");
    setShowAlert(false);

    // 重置网速相关状态
    setIsMultipartUpload(false);
    setUploadSpeed(0);
    setEstimatedTimeRemaining(0);
    uploadStartTimeRef.current = null;
    lastProgressTimeRef.current = null;
    lastBytesUploadedRef.current = 0;
  };

  // 10. 返回与原有 hook 完全相同的 API
  return {
    // State
    selectedFile,
    setSelectedFile,
    uploadProgress,
    uploadState,
    fileDuration,
    setFileDuration,
    transcriptionFileId,
    setTranscriptionFileId,
    showAlert,
    errorMessage,
    processingProgress,
    processing,

    // 新增：网速和剩余时间状态
    uploadSpeed,
    estimatedTimeRemaining,
    isMultipartUpload,

    // Methods
    uploadFile,
    cancelUpload,
    resetUploadStates,
    setShowAlert,
    setErrorMessage,
    setUploadState,

    // 标识这是 Uppy 版本
    isUppyVersion: true,
  };
};
