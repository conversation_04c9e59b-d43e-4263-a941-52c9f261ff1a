import { useState, useCallback } from "react";
import { userService } from "@/services/api/userService";

/**
 * Hook for checking user limits before allowing upload/youtube actions
 * @returns {Object} - Contains checkLimits function and loading state
 */
export const useUserLimits = () => {
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Check if user has sufficient limits for transcription
   * @returns {Object} - { canProceed: boolean, limitType: string|null }
   */
  const checkLimits = useCallback(async () => {
    setIsLoading(true);
    
    try {
      const response = await userService.getUserLimits();
      const { transcriptionMinutesRemaining, dailyTranscriptionCountRemaining } = response.data;

      // Check if user has exceeded limits
      if (transcriptionMinutesRemaining <= 0) {
        return {
          canProceed: false,
          limitType: "minutes",
        };
      }

      if (dailyTranscriptionCountRemaining <= 0) {
        return {
          canProceed: false,
          limitType: "daily_count",
        };
      }

      // User has sufficient limits
      return {
        canProceed: true,
        limitType: null,
      };
    } catch (error) {
      console.error("Failed to check user limits:", error);
      // On error, allow user to proceed (fail open)
      return {
        canProceed: true,
        limitType: null,
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    checkLimits,
    isLoading,
  };
};
