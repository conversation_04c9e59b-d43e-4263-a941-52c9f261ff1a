import { useState, useEffect, useCallback } from "react";
import { useAudioLanguage } from "@/hooks/useAudioLanguage";
import { storageService } from "@/services/storageService";
import { transcriptionService } from "@/services/api/transcriptionService";
import { useAuthStore } from "@/stores/useAuthStore";

/**
 * 转录设置管理Hook
 * 统一管理语言选择、字幕生成、说话人识别等转录相关设置
 */
export const useTranscriptionSettings = (options = {}) => {
  const {
    // 是否自动初始化设置（从localStorage读取）
    autoInitialize = true,
    // 转录文件ID，用于API更新
    transcriptionFileId = null,
    // 初始设置值
    initialSettings = {},
  } = options;

  const { user } = useAuthStore();
  const { getCurrentLanguage, handleLanguageChange } = useAudioLanguage();

  // 语言选择状态
  const [selectedLanguage, setSelectedLanguage] = useState(
    initialSettings.selectedLanguage || getCurrentLanguage()
  );

  // 字幕生成状态
  const [subtitleEnabled, setSubtitleEnabled] = useState(
    initialSettings.subtitleEnabled ?? false
  );

  // 说话人识别状态
  const [enableSpeakerDiarization, setEnableSpeakerDiarization] = useState(
    initialSettings.enableSpeakerDiarization ?? false
  );

  // 高级设置展开状态
  const [advancedSettingsOpen, setAdvancedSettingsOpen] = useState(
    initialSettings.advancedSettingsOpen ?? false
  );

  // Premium功能提示Dialog状态
  const [showPremiumDialog, setShowPremiumDialog] = useState(false);

  // 待应用的说话人识别设置（用于文件上传完成后应用）
  const [pendingSpeakerRecognition, setPendingSpeakerRecognition] =
    useState(null);

  // 组件挂载时自动初始化设置
  useEffect(() => {
    if (!autoInitialize) return;

    // 从storageService读取字幕设置
    const storedSubtitlePreference = storageService.getSubtitlePreference();
    setSubtitleEnabled(storedSubtitlePreference);

    // 确保语言设置已经保存到localStorage
    const currentLang = getCurrentLanguage();
    handleLanguageChange(currentLang);
    setSelectedLanguage(currentLang);
  }, [autoInitialize, getCurrentLanguage, handleLanguageChange]);

  // 处理语言选择
  const handleLanguageSelect = useCallback(
    (code) => {
      setSelectedLanguage(code);
      handleLanguageChange(code);

      // 如果有transcriptionFileId，还需要调用API更新
      if (transcriptionFileId) {
        transcriptionService.updateLanguageCode(transcriptionFileId, code);
      }
    },
    [handleLanguageChange, transcriptionFileId]
  );

  // 处理字幕开关
  const handleSubtitleChange = useCallback(
    (enabled) => {
      setSubtitleEnabled(enabled);

      // 使用storageService保存设置
      storageService.setSubtitlePreference(enabled);

      // 如果有transcriptionFileId，还需要调用API更新
      if (transcriptionFileId) {
        const transcriptionType = enabled ? "subtitle" : "transcript";
        transcriptionService.updateTranscriptionType(
          transcriptionFileId,
          transcriptionType
        );
      }
    },
    [transcriptionFileId]
  );

  // 处理说话人识别开关
  const handleSpeakerDiarizationChange = useCallback(
    (enabled) => {
      // 检查是否为付费用户
      if (enabled && !user?.hasPaidPlan) {
        setShowPremiumDialog(true);
        return;
      }

      setEnableSpeakerDiarization(enabled);

      // 如果有transcriptionFileId，立即应用设置
      if (transcriptionFileId) {
        transcriptionService.updateSpeakerRecognition(
          transcriptionFileId,
          enabled
        );
      } else {
        // 如果没有transcriptionFileId，保存为待应用的设置
        setPendingSpeakerRecognition(enabled);
      }
    },
    [user?.hasPaidPlan, transcriptionFileId]
  );

  // 处理高级设置展开/收起
  const handleAdvancedSettingsToggle = useCallback(() => {
    setAdvancedSettingsOpen((prev) => !prev);
  }, []);

  // 重置所有设置到默认值
  const resetSettings = useCallback(() => {
    const storedSubtitlePreference = storageService.getSubtitlePreference();
    setSubtitleEnabled(storedSubtitlePreference);

    const currentLang = getCurrentLanguage();
    setSelectedLanguage(currentLang);

    setEnableSpeakerDiarization(false);
    setAdvancedSettingsOpen(false);
    setShowPremiumDialog(false);
    setPendingSpeakerRecognition(null);
  }, [getCurrentLanguage]);

  // 当文件处理完成时应用待处理的设置
  const applyPendingSettings = useCallback(
    (fileId) => {
      if (pendingSpeakerRecognition !== null && fileId) {
        transcriptionService.updateSpeakerRecognition(
          fileId,
          pendingSpeakerRecognition
        );
        setPendingSpeakerRecognition(null);
      }
    },
    [pendingSpeakerRecognition]
  );

  // 获取当前所有设置的值
  const getSettings = useCallback(() => {
    return {
      selectedLanguage,
      subtitleEnabled,
      enableSpeakerDiarization,
      advancedSettingsOpen,
    };
  }, [
    selectedLanguage,
    subtitleEnabled,
    enableSpeakerDiarization,
    advancedSettingsOpen,
  ]);

  // 批量更新设置
  const updateSettings = useCallback(
    (newSettings) => {
      if (newSettings.selectedLanguage !== undefined) {
        handleLanguageSelect(newSettings.selectedLanguage);
      }
      if (newSettings.subtitleEnabled !== undefined) {
        handleSubtitleChange(newSettings.subtitleEnabled);
      }
      if (newSettings.enableSpeakerDiarization !== undefined) {
        handleSpeakerDiarizationChange(newSettings.enableSpeakerDiarization);
      }
      if (newSettings.advancedSettingsOpen !== undefined) {
        setAdvancedSettingsOpen(newSettings.advancedSettingsOpen);
      }
    },
    [handleLanguageSelect, handleSubtitleChange, handleSpeakerDiarizationChange]
  );

  return {
    // 状态值
    selectedLanguage,
    subtitleEnabled,
    enableSpeakerDiarization,
    advancedSettingsOpen,
    showPremiumDialog,
    pendingSpeakerRecognition,

    // 处理函数
    handleLanguageSelect,
    handleSubtitleChange,
    handleSpeakerDiarizationChange,
    handleAdvancedSettingsToggle,
    setShowPremiumDialog,

    // 工具函数
    resetSettings,
    applyPendingSettings,
    getSettings,
    updateSettings,
  };
};
