import { useState, useCallback, useRef } from "react";
import { transcriptionService } from "@/services/api/transcriptionService";

// 定义常量
const POLLING_INTERVAL = 3000; // 轮询间隔，单位毫秒
const MAX_POLLING_TIME = 10 * 60 * 1000; // 最大轮询时间，10分钟

/**
 * Hook for managing task retry and polling
 * @param {Function} onTaskStatusUpdate - Callback when task status updates
 * @param {Function} onTaskComplete - Callback when task completes (success or failure)
 */
export const useTaskRetry = (onTaskStatusUpdate, onTaskComplete) => {
  const [retryingTasks, setRetryingTasks] = useState(new Set());
  const [taskRetryInfo, setTaskRetryInfo] = useState(new Map()); // 存储任务重试信息
  const pollingIntervals = useRef(new Map());

  // Start polling for a specific task
  const startTaskPolling = useCallback(
    (taskId, taskType) => {
      // Clear existing polling for this task if any
      if (pollingIntervals.current.has(taskId)) {
        clearInterval(pollingIntervals.current.get(taskId));
      }

      const pollInterval = setInterval(async () => {
        try {
          const response = await transcriptionService.getTaskStatus(taskId);
          const taskStatus = response.data;

          // Update task status
          if (onTaskStatusUpdate) {
            onTaskStatusUpdate(taskType, taskStatus);
          }

          // 保存重试信息（如果有的话）
          if (
            taskStatus.retryCount !== undefined ||
            taskStatus.maxRetryCount !== undefined
          ) {
            setTaskRetryInfo((prev) => {
              const newMap = new Map(prev);
              newMap.set(taskId, {
                retryCount: taskStatus.retryCount || 0,
                maxRetryCount: taskStatus.maxRetryCount || 0,
              });
              return newMap;
            });
          }

          // Check if task is completed (success or failure)
          if (
            taskStatus.status === "completed" ||
            taskStatus.status === "failed"
          ) {
            // Stop polling
            clearInterval(pollInterval);
            pollingIntervals.current.delete(taskId);

            // Remove from retrying tasks
            setRetryingTasks((prev) => {
              const newSet = new Set(prev);
              newSet.delete(taskId);
              return newSet;
            });

            // Call completion callback
            if (onTaskComplete) {
              onTaskComplete(taskType, taskStatus);
            }
          }
        } catch (error) {
          console.error(`Failed to poll task ${taskId}:`, error);
          // Stop polling on error
          clearInterval(pollInterval);
          pollingIntervals.current.delete(taskId);

          // Remove from retrying tasks
          setRetryingTasks((prev) => {
            const newSet = new Set(prev);
            newSet.delete(taskId);
            return newSet;
          });
        }
      }, POLLING_INTERVAL);

      // Store the interval
      pollingIntervals.current.set(taskId, pollInterval);

      // Set maximum polling time (10 minutes)
      setTimeout(() => {
        if (pollingIntervals.current.has(taskId)) {
          clearInterval(pollingIntervals.current.get(taskId));
          pollingIntervals.current.delete(taskId);

          setRetryingTasks((prev) => {
            const newSet = new Set(prev);
            newSet.delete(taskId);
            return newSet;
          });

          console.warn(`Polling timeout for task ${taskId}`);
        }
      }, MAX_POLLING_TIME);
    },
    [onTaskStatusUpdate, onTaskComplete]
  );

  // Retry a task
  const retryTask = useCallback(
    async (taskId, taskType) => {
      if (retryingTasks.has(taskId)) {
        return; // Already retrying
      }

      try {
        // Mark as retrying
        setRetryingTasks((prev) => new Set(prev).add(taskId));

        // Call retry API
        await transcriptionService.retryTask(taskId);

        // Start polling for status updates
        startTaskPolling(taskId, taskType);
      } catch (error) {
        console.error(`Failed to retry task ${taskId}:`, error);

        // Remove from retrying tasks on error
        setRetryingTasks((prev) => {
          const newSet = new Set(prev);
          newSet.delete(taskId);
          return newSet;
        });

        throw error;
      }
    },
    [retryingTasks, startTaskPolling]
  );

  // Check if a task is currently being retried
  const isTaskRetrying = useCallback(
    (taskId) => {
      return retryingTasks.has(taskId);
    },
    [retryingTasks]
  );

  // Get retry information for a task
  const getTaskRetryInfo = useCallback(
    (taskId) => {
      return taskRetryInfo.get(taskId) || null;
    },
    [taskRetryInfo]
  );

  // Clean up all polling intervals and retrying tasks state
  const cleanupTaskPolling = useCallback(() => {
    pollingIntervals.current.forEach((interval) => {
      clearInterval(interval);
    });
    pollingIntervals.current.clear();
    setRetryingTasks(new Set());
    setTaskRetryInfo(new Map());
  }, []);

  return {
    retryTask,
    isTaskRetrying,
    getTaskRetryInfo,
    cleanupTaskPolling,
  };
};
