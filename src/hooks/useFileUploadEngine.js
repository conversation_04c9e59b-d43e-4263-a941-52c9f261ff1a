import { useEffect } from "react";
import { UPLOAD_CONFIG } from "@/config/upload";
import { useUppyFileUpload } from "@/hooks/useUppyFileUpload";

/**
 * 文件上传引擎 Hook
 *
 * 使用 Uppy 作为唯一的上传引擎
 * 对外提供统一的 API，确保 UI 组件无需修改
 */
export const useFileUploadEngine = () => {
  // 使用 Uppy 引擎
  const uppyUpload = useUppyFileUpload();

  // 输出调试日志
  useEffect(() => {
    if (UPLOAD_CONFIG.debug) {
      console.log("🚀 Using Uppy upload engine");
    }
  }, []); // 只在组件挂载时执行一次

  // 返回 Uppy 引擎
  return {
    ...uppyUpload,
    engineType: "uppy",
  };
};
