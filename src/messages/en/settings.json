{"page": {"title": "Settings", "subtitle": "Manage your account settings and preferences", "backButton": "Back"}, "navigation": {"profile": "Profile", "preferences": "Preferences", "usage": "Usage", "apiKeyManagement": "API Keys", "notifications": "Notifications", "dangerZone": "Danger Zone"}, "profile": {"title": "Profile", "description": "Manage your personal information", "firstName": "First Name", "lastName": "Last Name", "emailAddress": "Email Address", "save": "Save", "saving": "Saving...", "saved": "Saved!", "successMessage": "Profile saved successfully", "errorMessage": "Failed to save profile. Please try again."}, "preferences": {"title": "Preferences", "description": "Manage your application preferences", "language": {"label": "Interface Language", "description": "Choose your preferred language for the application interface"}, "timezone": {"label": "Time Zone", "description": "Select your time zone", "placeholder": "Select timezone...", "searchPlaceholder": "Search timezone...", "loading": "Loading timezones...", "successMessage": "Timezone updated successfully", "errorMessage": "Failed to update timezone. Please try again."}}, "usage": {"title": "Usage", "description": "View your account usage and remaining credits"}, "apiKeyManagement": {"title": "API Key Management", "description": "Manage your API keys for accessing the UniScribe API", "requiresSubscription": "API access requires an active subscription or LTD plan", "upgradePrompt": "Upgrade your plan to access API management features", "createKey": "Create New Key", "createFirstKey": "Create Your First API Key", "noKeys": "No API Keys", "noKeysDescription": "You haven't created any API keys yet.", "maxKeysReached": "Maximum API keys limit reached (5 keys)", "maxKeysDescription": "Delete an existing API key before creating a new one", "keyName": "Key Name", "keyNamePlaceholder": "Enter a name for your API key", "expiration": "Expiration", "noExpiration": "No expiration", "days": "days", "createdAt": "Created", "lastUsed": "Last Used", "neverUsed": "Never used", "actions": "Actions", "rename": "<PERSON><PERSON>", "reset": "Reset", "delete": "Delete", "copy": "Copy", "copied": "Copied!", "active": "Active", "expired": "Expired", "keyPreview": "Key Preview", "fullKey": "Full API Key", "keyWarning": "This is the only time you'll see the full API key. Store it securely.", "keyStorageWarning": "If you lose this key, you'll need to reset it to get a new one.", "confirmDelete": "Are you sure you want to delete this API key?", "confirmDeleteDescription": "This action cannot be undone. All applications using this key will stop working immediately.", "confirmReset": "Are you sure you want to reset this API key?", "confirmResetDescription": "This will generate a new key value. The old key will stop working immediately.", "createDialog": {"title": "Create API Key", "nameLabel": "Key Name", "namePlaceholder": "e.g., Production API Key", "expirationLabel": "Expiration (optional)", "expirationPlaceholder": "Number of days", "expirationHelp": "Leave empty for no expiration (1-3650 days)", "cancel": "Cancel", "create": "Create Key", "creating": "Creating..."}, "renameDialog": {"title": "Rename API Key", "description": "Change the name of your API key.", "nameLabel": "New Name", "cancel": "Cancel", "save": "Save", "saving": "Saving..."}, "keyCreatedDialog": {"title": "API Key Created Successfully", "copyButton": "Copy API Key", "close": "Close"}, "keyResetDialog": {"title": "API Key Reset Successfully", "copyButton": "Copy New API Key", "close": "Close"}, "successMessages": {"keyCreated": "API key created successfully", "keyUpdated": "API key updated successfully", "keyReset": "API key reset successfully", "keyDeleted": "API key deleted successfully"}, "errorMessages": {"loadFailed": "Failed to load API keys. Please try again.", "createFailed": "Failed to create API key. Please try again.", "updateFailed": "Failed to update API key. Please try again.", "resetFailed": "Failed to reset API key. Please try again.", "deleteFailed": "Failed to delete API key. Please try again.", "accessDenied": "API access requires active subscription or LTD plan", "maxKeysReached": "Maximum number of API keys reached (5)", "invalidName": "API key name must be between 1 and 100 characters", "invalidExpiration": "Expiration must be between 1 and 3650 days", "keyNotFound": "API key not found", "nameExists": "API key name already exists"}, "upgradePlan": "Upgrade Plan", "viewApiDocs": "View API Documentation"}, "notifications": {"title": "Email Notifications", "description": "Manage your email notification preferences", "transcriptionSuccess": {"label": "Transcription Success Notifications", "description": "Receive notifications when your audio transcription is complete"}, "quotaReset": {"label": "Transcription Quota Reset Notifications", "description": "Receive notifications when your transcription quota is reset"}, "productUpdates": {"label": "Product Updates and New Features", "description": "Receive notifications about product updates and new features"}, "successMessage": "Notification settings updated", "errorMessage": "Failed to update notification settings. Please try again."}, "dangerZone": {"title": "Danger Zone", "description": "Actions that cannot be undone", "deleteAccount": {"label": "Delete Account", "description": "Permanently delete your account and all data", "button": "Delete Account", "dialog": {"title": "Delete Account", "description": "Are you sure you want to delete your account?", "warning": "This action cannot be undone.", "consequences": {"title": "When you delete your account:", "items": ["All your data will be permanently deleted", "You will lose access to all your transcriptions", "Your subscription will be canceled (if applicable)", "Your account cannot be recovered", "Your email will no longer be able to create a new account"]}, "confirmation": {"label": "To confirm, type DELETE below:", "placeholder": "Type DELETE here"}, "buttons": {"cancel": "Cancel", "delete": "Delete", "deleting": "Deleting..."}, "successMessage": "Your account has been deleted", "errorMessage": "Failed to delete account. Please try again."}}}}