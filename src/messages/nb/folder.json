{"dialog": {"create": {"title": "<PERSON><PERSON><PERSON><PERSON> map<PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "loading": "Oppretter..."}, "edit": {"title": "Gi navn til mappe", "submit": "Lagre", "loading": "Lagrer..."}, "delete": {"title": "<PERSON><PERSON> mappe", "description": "Du er i ferd med å slette mappen \"{folderName}\".", "warning": "⚠️ Å slette denne mappen vil også permanent slette alle transkripsjoner inni. Dette kan ikke angres.", "submit": "<PERSON><PERSON>", "loading": "Sletter..."}, "move": {"title": "<PERSON>tt til mappe", "uncategorized": "<PERSON><PERSON> kate<PERSON>i", "uncategorizedTooltip": "Ikke-kategoriserte transkripsjoner tilhører ingen mappe og kan vises i Alle.", "submit": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading": "Flytting...", "moveSuccess": "Fil flyttet vellykket", "moveError": "<PERSON><PERSON> ikke flytte filen", "batchMoveSuccess": "{count, plural, =1 {1 fil flyttet med suksess} other {# filer flyttet med suksess}}", "batchMoveError": "<PERSON><PERSON> ikke flytte filer", "movingSelectedFiles": "Flytter {count} valgte filer", "movingFromFolder": "Flytter \"{filename}\" fra {folderName}", "noFileSelected": "Ingen fil valgt", "currentFolder": "(Nåværende)", "noFoldersAvailable": "Ingen mapper til<PERSON>g"}, "batchMoveResult": {"successTitle": "<PERSON><PERSON> fullført", "partialSuccessTitle": "<PERSON><PERSON> delvis fullført", "failureTitle": "Flytting mislyktes", "total": "Total", "moved": "Flyttet", "failed": "Mislyktes", "successMessage": "{count, plural, =1 {1 transkripsjon flyttet vellykket} other {# transkripsjoner flyttet vellykket}}", "errorDetails": "<PERSON><PERSON>:", "close": "Lukk"}, "common": {"folderName": "Mappenavn", "placeholder": "Skriv inn mappenavn", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "characterLimit": "Maksimalt 40 tegn, nåværende: {current}/40", "characterLimitError": "Mappenavn kan ikke overstige 40 tegn"}}, "list": {"actions": {"rename": "Gi ny navn", "delete": "<PERSON><PERSON>"}}}